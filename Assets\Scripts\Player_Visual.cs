using System.Linq;
using UnityEngine;

public class Player_Visual : MonoBehaviour
{
    [SerializeField] private float bullet_speed = 10f;
    private float nextActionTime = 0.0f;
    [SerializeField] private float bullet_cooldown = 0.5f;
    [SerializeField] private Transform bullet_instantiate_position;
    private Bullet_Spawner bullet_Spawner;

    private Transform[] nearby_enemies = new Transform[30];

    void Awake()
    {
        bullet_Spawner = transform.parent.GetComponent<Bullet_Spawner>();
    }

    // Update is called once per frame
    void Update()
    {
        if (Time.time > nextActionTime)
        {
            nextActionTime = Time.time + bullet_cooldown;
            InstantiateBullet();
        }

    }


    void InstantiateBullet()
    {
        var b = bullet_Spawner.GetBullet();
        b.transform.position = bullet_instantiate_position.position;
        b.transform.rotation = Quaternion.Euler(90, 0, 0);
        b.GetComponent<Rigidbody>().AddForce(bullet_instantiate_position.forward * bullet_speed, ForceMode.Impulse);
    }

    void OnTriggerEnter(Collider other)
    {
        nearby_enemies.Append(other.transform);
    }
}
