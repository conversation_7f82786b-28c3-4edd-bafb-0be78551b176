using System.Collections.Generic;
using UnityEngine;

public class Player_Visual : MonoBehaviour
{
    [SerializeField] private float bullet_speed = 10f;
    private float nextActionTime = 0.0f;
    [SerializeField] private float bullet_cooldown = 0.5f;
    [SerializeField] private Transform bullet_instantiate_position;
    private Bullet_Spawner bullet_Spawner;

    // Enemy targeting system
    private readonly List<Transform> nearby_enemies = new();
    [SerializeField] private float rotation_speed = 5f;

    void Awake()
    {
        bullet_Spawner = transform.parent.GetComponent<Bullet_Spawner>();
    }

    // Update is called once per frame
    void Update()
    {
        // Clean up destroyed enemies
        CleanupDestroyedEnemies();

        // Handle targeting and rotation
        HandleTargeting();

        // Handle shooting
        if (Time.time > nextActionTime)
        {
            nextActionTime = Time.time + bullet_cooldown;
            InstantiateBullet();
        }
    }


    void InstantiateBullet()
    {
        var b = bullet_Spawner.GetBullet();
        b.transform.SetPositionAndRotation(bullet_instantiate_position.position, Quaternion.Euler(90, 0, 0));
        b.GetComponent<Rigidbody>().AddForce(bullet_instantiate_position.forward * bullet_speed, ForceMode.Impulse);
    }

    void OnTriggerEnter(Collider other)
    {
        // Check if the object entering is an enemy
        if (other.CompareTag("Enemy"))
        {
            // Add enemy to the list if it's not already there
            if (!nearby_enemies.Contains(other.transform))
            {
                nearby_enemies.Add(other.transform);
                Debug.Log($"Enemy entered trigger. Total enemies: {nearby_enemies.Count}");
            }
        }
    }

    void OnTriggerExit(Collider other)
    {
        // Check if the object leaving is an enemy
        if (other.CompareTag("Enemy"))
        {
            // Remove enemy from the list
            if (nearby_enemies.Contains(other.transform))
            {
                nearby_enemies.Remove(other.transform);
                Debug.Log($"Enemy left trigger. Total enemies: {nearby_enemies.Count}");
            }
        }
    }

    void CleanupDestroyedEnemies()
    {
        // Remove any null or destroyed enemies from the list
        for (int i = nearby_enemies.Count - 1; i >= 0; i--)
        {
            if (nearby_enemies[i] == null)
            {
                nearby_enemies.RemoveAt(i);
            }
        }
    }

    void HandleTargeting()
    {
        if (nearby_enemies.Count > 0)
        {
            // Target the first enemy in the list (index 0)
            Transform target = nearby_enemies[0];

            if (target != null)
            {
                // Calculate direction to target
                Vector3 direction = (target.position - transform.position).normalized;

                // Calculate target rotation
                Quaternion targetRotation = Quaternion.LookRotation(direction);

                // Smoothly rotate towards the target
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotation_speed * Time.deltaTime);
            }
        }
        else
        {
            // No enemies nearby, look forward
            Quaternion defaultRotation = Quaternion.LookRotation(Vector3.forward);
            transform.rotation = Quaternion.Slerp(transform.rotation, defaultRotation, rotation_speed * Time.deltaTime);
        }
    }
}
