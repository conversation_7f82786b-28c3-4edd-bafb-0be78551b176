using System;
using UnityEngine;

public class Bullet : MonoBehaviour
{
    private Action<Bullet> killAction;
    private Rigidbody rb;

    [SerializeField] private float bullet_lifetime = 1f;

    void Awake()
    {
        // Cache the Rigidbody component
        rb = GetComponent<Rigidbody>();
    }

    public void SetKillAction(Action<Bullet> action)
    {
        killAction = action;
    }

    void OnEnable()
    {
        // Reset the bullet's rigidbody velocity when reused
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        // Use Invoke to call DeactivateBullet after bullet_lifetime
        Invoke(nameof(DeactivateBullet), bullet_lifetime);
    }

    void OnDisable()
    {
        // Cancel any pending Invoke calls when bullet is disabled
        CancelInvoke(nameof(DeactivateBullet));
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.CompareTag("Reward") || collision.gameObject.CompareTag("Enemy"))
        {
           DeactivateBullet();
        }
    }
    
    // New method to handle deactivation
    void DeactivateBullet()
    {
        if (gameObject.activeInHierarchy)
        {
            killAction?.Invoke(this);
        }
    }

    // Method to force deactivation (called by pool when reusing oldest bullet)
    public void ForceDeactivate()
    {
        // Cancel any pending Invoke calls
        CancelInvoke(nameof(DeactivateBullet));

        if (gameObject.activeInHierarchy)
        {
            killAction?.Invoke(this);
        }
    }

    void OnDestroy()
    {
        // Only call killAction if it hasn't been called already
        if (gameObject.activeInHierarchy && killAction != null)
        {
            killAction(this);
        }
    }
}
