using System;
using UnityEngine;

public class Bullet : MonoBehaviour
{
    private Action<Bullet> killAction;

    [SerializeField] private float bullet_lifetime = 1f;

    public void SetKillAction(Action<Bullet> action)
    {
        killAction = action;
    }

    void Awake()
    {
        // Use Invoke to call DeactivateBullet instead of On<PERSON><PERSON>roy
        Invoke("DeactivateBullet", bullet_lifetime);
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.tag == "Reward")
        {
           DeactivateBullet();
        }
    }
    
    // New method to handle deactivation
    void DeactivateBullet()
    {
        if (gameObject.activeInHierarchy)
        {
            killAction?.Invoke(this);
            gameObject.SetActive(false);
        }
    }

    void OnDestroy()
    {
        // Only call killAction if it hasn't been called already
        if (gameObject.activeInHierarchy && killAction != null)
        {
            killAction(this);
        }
    }
}
