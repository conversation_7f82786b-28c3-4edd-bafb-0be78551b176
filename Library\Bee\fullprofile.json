{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2724, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2724, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2724, "tid": 38, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2724, "tid": 38, "ts": 1749719409978940, "dur": 828, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2724, "tid": 38, "ts": 1749719409985880, "dur": 682, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2724, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2724, "tid": 1, "ts": 1749719409608641, "dur": 4866, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2724, "tid": 1, "ts": 1749719409613511, "dur": 60691, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2724, "tid": 1, "ts": 1749719409674212, "dur": 35100, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2724, "tid": 38, "ts": 1749719409986566, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 2724, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409606781, "dur": 7414, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409614198, "dur": 356324, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409615272, "dur": 2241, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409617517, "dur": 1540, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619060, "dur": 435, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619497, "dur": 11, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619509, "dur": 63, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619576, "dur": 2, "ph": "X", "name": "ProcessMessages 2827", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619579, "dur": 40, "ph": "X", "name": "ReadAsync 2827", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619621, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619623, "dur": 29, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619655, "dur": 28, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619686, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619718, "dur": 26, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619746, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619748, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619773, "dur": 23, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619800, "dur": 26, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619828, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619875, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619878, "dur": 28, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619908, "dur": 27, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619937, "dur": 33, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619972, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409619973, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620003, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620005, "dur": 32, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620040, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620076, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620078, "dur": 31, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620112, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620136, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620161, "dur": 28, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620192, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620195, "dur": 33, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620230, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620231, "dur": 34, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620268, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620270, "dur": 27, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620299, "dur": 28, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620329, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620331, "dur": 27, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620359, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620361, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620386, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620414, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620415, "dur": 26, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620443, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620444, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620472, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620496, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620521, "dur": 104, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620628, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620630, "dur": 45, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620677, "dur": 1, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620679, "dur": 28, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620710, "dur": 23, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620735, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620761, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620763, "dur": 30, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620795, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620796, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620826, "dur": 23, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620852, "dur": 29, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620883, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620885, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620912, "dur": 29, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620944, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620969, "dur": 28, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620998, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409620999, "dur": 26, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621028, "dur": 24, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621055, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621080, "dur": 23, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621106, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621135, "dur": 30, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621168, "dur": 25, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621195, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621196, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621226, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621256, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621285, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621286, "dur": 25, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621315, "dur": 26, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621343, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621344, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621372, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621398, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621429, "dur": 31, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621463, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621494, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621521, "dur": 32, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621554, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621582, "dur": 31, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621615, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621616, "dur": 30, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621649, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621675, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621702, "dur": 25, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621729, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621731, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621759, "dur": 21, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621782, "dur": 25, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621809, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621810, "dur": 26, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621839, "dur": 59, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621901, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621926, "dur": 26, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621953, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621954, "dur": 25, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621981, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409621982, "dur": 30, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622014, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622038, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622042, "dur": 122, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622166, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622200, "dur": 27, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622230, "dur": 26, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622258, "dur": 30, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622291, "dur": 24, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622318, "dur": 29, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622350, "dur": 38, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622389, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622391, "dur": 27, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622420, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622421, "dur": 28, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622452, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622479, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622501, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622524, "dur": 25, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622550, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622551, "dur": 28, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622582, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622604, "dur": 25, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622632, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622658, "dur": 20, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622680, "dur": 24, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622706, "dur": 24, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622733, "dur": 29, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622763, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622764, "dur": 29, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622796, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622821, "dur": 26, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622849, "dur": 24, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622874, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622876, "dur": 26, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622904, "dur": 22, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622928, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622954, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409622981, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623003, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623029, "dur": 23, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623053, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623055, "dur": 25, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623083, "dur": 22, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623108, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623130, "dur": 24, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623157, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623186, "dur": 24, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623212, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623234, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623261, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623287, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623314, "dur": 22, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623339, "dur": 27, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623368, "dur": 24, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623393, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623394, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623417, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623442, "dur": 26, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623471, "dur": 25, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623499, "dur": 28, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623529, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623552, "dur": 23, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623578, "dur": 22, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623602, "dur": 24, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623627, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623629, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623652, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623680, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623682, "dur": 28, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623713, "dur": 36, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623751, "dur": 27, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623781, "dur": 25, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623809, "dur": 31, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623842, "dur": 26, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623871, "dur": 28, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623902, "dur": 30, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623934, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623966, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409623998, "dur": 24, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624024, "dur": 27, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624054, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624082, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624106, "dur": 22, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624130, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624155, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624156, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624188, "dur": 39, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624229, "dur": 20, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624251, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624275, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624276, "dur": 30, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624310, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624341, "dur": 22, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624365, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624393, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624421, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624449, "dur": 22, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624473, "dur": 31, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624506, "dur": 26, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624535, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624563, "dur": 20, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624585, "dur": 25, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624613, "dur": 28, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624642, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624643, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624670, "dur": 27, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624700, "dur": 24, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624725, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624727, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624751, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624777, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624800, "dur": 38, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624839, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624840, "dur": 27, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624870, "dur": 23, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624896, "dur": 20, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624918, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624945, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624971, "dur": 23, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409624997, "dur": 29, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625028, "dur": 24, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625055, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625076, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625101, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625128, "dur": 24, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625155, "dur": 30, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625188, "dur": 24, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625214, "dur": 27, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625245, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625277, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625279, "dur": 28, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625309, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625336, "dur": 26, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625365, "dur": 51, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625418, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625448, "dur": 25, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625475, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625501, "dur": 26, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625529, "dur": 21, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625553, "dur": 26, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625581, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625610, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625611, "dur": 25, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625639, "dur": 25, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625666, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625668, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625693, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625717, "dur": 25, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625745, "dur": 31, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625778, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625779, "dur": 24, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625806, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625830, "dur": 21, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625854, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625887, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625889, "dur": 26, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625919, "dur": 22, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625943, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625944, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409625976, "dur": 24, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626001, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626002, "dur": 32, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626037, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626068, "dur": 26, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626097, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626098, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626126, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626154, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626156, "dur": 22, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626180, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626210, "dur": 26, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626239, "dur": 24, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626279, "dur": 27, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626307, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626308, "dur": 25, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626336, "dur": 27, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626364, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626365, "dur": 25, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626393, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626419, "dur": 26, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626446, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626448, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626473, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626502, "dur": 48, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626553, "dur": 50, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626605, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626606, "dur": 27, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626636, "dur": 29, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626667, "dur": 25, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626695, "dur": 26, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626723, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626724, "dur": 30, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626756, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626758, "dur": 21, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626781, "dur": 24, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626807, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626835, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626863, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626897, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626921, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626923, "dur": 28, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626955, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409626986, "dur": 27, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627016, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627039, "dur": 53, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627094, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627096, "dur": 25, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627124, "dur": 26, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627152, "dur": 35, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627189, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627191, "dur": 35, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627227, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627229, "dur": 30, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627261, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627264, "dur": 31, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627297, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627299, "dur": 29, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627330, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627334, "dur": 37, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627372, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627374, "dur": 29, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627404, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627406, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627433, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627450, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627480, "dur": 25, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627507, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627508, "dur": 25, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627535, "dur": 31, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627569, "dur": 25, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627596, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627597, "dur": 52, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627652, "dur": 26, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627681, "dur": 22, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627705, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627730, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627732, "dur": 30, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627765, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627786, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627812, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627838, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627840, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627866, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627894, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627924, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627950, "dur": 25, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627977, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409627979, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628005, "dur": 26, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628034, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628062, "dur": 25, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628088, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628090, "dur": 25, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628118, "dur": 23, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628144, "dur": 26, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628172, "dur": 26, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628201, "dur": 36, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628239, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628262, "dur": 27, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628291, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628293, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628325, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628354, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628406, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628439, "dur": 26, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628468, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628498, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628500, "dur": 93, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628596, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628627, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628629, "dur": 30, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628661, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628663, "dur": 26, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628690, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628691, "dur": 25, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628718, "dur": 28, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628750, "dur": 24, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628777, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628802, "dur": 22, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628827, "dur": 87, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628917, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628950, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628952, "dur": 28, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409628982, "dur": 24, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629009, "dur": 74, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629086, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629115, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629117, "dur": 23, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629142, "dur": 105, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629251, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629282, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629283, "dur": 42, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629327, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629329, "dur": 84, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629417, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629451, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629452, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629481, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629508, "dur": 76, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629586, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629614, "dur": 29, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629646, "dur": 26, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629675, "dur": 70, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629747, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629777, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629805, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629833, "dur": 80, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629916, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629947, "dur": 27, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409629977, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630003, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630091, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630120, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630121, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630151, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630153, "dur": 80, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630237, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630274, "dur": 26, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630302, "dur": 29, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630334, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630335, "dur": 72, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630412, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630442, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630443, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630474, "dur": 81, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630559, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630589, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630590, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630617, "dur": 48, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630667, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630668, "dur": 59, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630730, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630761, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630762, "dur": 56, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630821, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630824, "dur": 44, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630872, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630874, "dur": 84, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409630962, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631002, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631004, "dur": 35, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631042, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631045, "dur": 34, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631082, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631153, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631198, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631200, "dur": 36, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631238, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631240, "dur": 83, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631328, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631364, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631366, "dur": 30, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631398, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631400, "dur": 81, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631485, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631514, "dur": 28, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631544, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631547, "dur": 24, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631574, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631645, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631679, "dur": 33, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631715, "dur": 31, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631748, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631750, "dur": 34, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631785, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631787, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631813, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631836, "dur": 24, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631864, "dur": 22, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631888, "dur": 27, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631918, "dur": 71, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409631992, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632030, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632032, "dur": 29, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632063, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632064, "dur": 23, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632089, "dur": 72, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632163, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632193, "dur": 32, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632228, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632230, "dur": 29, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632262, "dur": 77, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632342, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632372, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632374, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632403, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632404, "dur": 27, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632433, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632460, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632490, "dur": 2, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632493, "dur": 25, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632521, "dur": 24, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632547, "dur": 72, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632622, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632656, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632657, "dur": 28, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632687, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632689, "dur": 23, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632715, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632792, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632824, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632826, "dur": 44, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632873, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632874, "dur": 29, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632904, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632906, "dur": 26, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632935, "dur": 27, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632964, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632965, "dur": 27, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632995, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409632996, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633077, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633107, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633134, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633136, "dur": 81, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633222, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633250, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633251, "dur": 24, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633278, "dur": 28, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633307, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633309, "dur": 27, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633338, "dur": 29, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633370, "dur": 34, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633407, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633408, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633440, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633441, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633509, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633538, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633567, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633590, "dur": 74, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633667, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633700, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633701, "dur": 25, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633728, "dur": 28, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633759, "dur": 73, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633834, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633863, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633864, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633894, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633897, "dur": 41, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633939, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633941, "dur": 29, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633972, "dur": 24, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409633999, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634023, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634050, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634135, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634167, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634169, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634196, "dur": 26, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634224, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634225, "dur": 63, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634290, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634318, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634320, "dur": 29, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634351, "dur": 78, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634431, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634459, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634461, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634488, "dur": 26, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634517, "dur": 27, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634545, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634547, "dur": 29, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634579, "dur": 24, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634606, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634633, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634660, "dur": 87, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634750, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634779, "dur": 37, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634819, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634821, "dur": 27, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634852, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634928, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634958, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409634960, "dur": 37, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635000, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635034, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635036, "dur": 31, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635069, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635070, "dur": 81, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635155, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635183, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635184, "dur": 30, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635216, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635218, "dur": 24, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635245, "dur": 62, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635310, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635341, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635342, "dur": 29, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635373, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635375, "dur": 75, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635452, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635484, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635485, "dur": 26, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635514, "dur": 24, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635541, "dur": 69, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635612, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635650, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635652, "dur": 30, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635685, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635687, "dur": 29, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635718, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635720, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635790, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635821, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635822, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635852, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635854, "dur": 87, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635945, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635975, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409635976, "dur": 49, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636027, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636028, "dur": 26, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636057, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636058, "dur": 62, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636124, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636159, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636161, "dur": 47, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636212, "dur": 29, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636243, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636245, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636313, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636343, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636344, "dur": 27, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636374, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636376, "dur": 75, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636454, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636486, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636488, "dur": 27, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636516, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636518, "dur": 82, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636603, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636631, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636632, "dur": 28, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636662, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636664, "dur": 27, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636693, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636695, "dur": 67, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636766, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636811, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636813, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636846, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636848, "dur": 26, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636878, "dur": 68, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636950, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636985, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409636987, "dur": 24, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637014, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637018, "dur": 83, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637105, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637139, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637140, "dur": 26, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637169, "dur": 26, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637198, "dur": 73, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637275, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637303, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637305, "dur": 28, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637336, "dur": 23, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637361, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637364, "dur": 77, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637444, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637475, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637500, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637530, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637531, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637569, "dur": 57, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637629, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637656, "dur": 28, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637687, "dur": 27, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637716, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637718, "dur": 74, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637795, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637827, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637853, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637856, "dur": 28, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637886, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637888, "dur": 75, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637966, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637995, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409637996, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638029, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638030, "dur": 22, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638054, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638056, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638132, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638158, "dur": 38, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638198, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638200, "dur": 26, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638228, "dur": 72, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638304, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638341, "dur": 26, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638369, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638371, "dur": 33, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638406, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638408, "dur": 22, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638432, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638510, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638539, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638541, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638570, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638572, "dur": 31, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638604, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638605, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638633, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638661, "dur": 1, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638663, "dur": 30, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638694, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638697, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638724, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638807, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638836, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638839, "dur": 28, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638868, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638870, "dur": 29, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638902, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638928, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638930, "dur": 32, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638963, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638965, "dur": 25, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409638993, "dur": 25, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639020, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639099, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639130, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639132, "dur": 31, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639165, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639166, "dur": 23, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639192, "dur": 26, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639221, "dur": 41, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639264, "dur": 2, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639267, "dur": 26, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639296, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639323, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639400, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639428, "dur": 136, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639570, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639598, "dur": 360, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409639962, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640026, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640031, "dur": 37, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640072, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640075, "dur": 34, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640112, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640114, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640159, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640162, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640202, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640205, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640251, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640255, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640299, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640302, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640346, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640348, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640435, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640438, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640488, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640492, "dur": 41, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640537, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640540, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640574, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640577, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640614, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640616, "dur": 37, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640657, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640660, "dur": 40, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640705, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640708, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640757, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640759, "dur": 36, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640798, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640802, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640837, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640840, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640880, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640883, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640926, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640929, "dur": 33, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640965, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409640968, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641011, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641014, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641051, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641055, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641099, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641102, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641142, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641145, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641188, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641192, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641233, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641236, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641268, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641271, "dur": 38, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641312, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641315, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641371, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641376, "dur": 38, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641417, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641421, "dur": 33, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641458, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641460, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641514, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641517, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641568, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641571, "dur": 43, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641618, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641621, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641667, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641670, "dur": 46, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641720, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641724, "dur": 57, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641785, "dur": 3, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641789, "dur": 42, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641835, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641838, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641877, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641880, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641919, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641922, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641970, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409641973, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642016, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642019, "dur": 43, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642065, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642067, "dur": 48, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642119, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642123, "dur": 46, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642172, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642176, "dur": 41, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642222, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642226, "dur": 54, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642283, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642286, "dur": 37, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642327, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642329, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642369, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642372, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642409, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642412, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642457, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642460, "dur": 35, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642501, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642505, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642548, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642552, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642585, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642588, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642615, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642695, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642697, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642731, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409642734, "dur": 5830, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648573, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648579, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648650, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648652, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648694, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648741, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648743, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648789, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409648791, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409649001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409649004, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409649045, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409649047, "dur": 1360, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650414, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650466, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650504, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650506, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650539, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650634, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409650666, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651021, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651068, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651070, "dur": 251, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651325, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651327, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651378, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651381, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651420, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651423, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651455, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651521, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651558, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651588, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651683, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409651715, "dur": 324, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652042, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652069, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652071, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652097, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652099, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652151, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652172, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652205, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652207, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652238, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652240, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652278, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652312, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652314, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652346, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652374, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652402, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652428, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652456, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652480, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652591, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652628, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652630, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652663, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652699, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652701, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652731, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652760, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652763, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409652792, "dur": 205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653001, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653024, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653053, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653106, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653143, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653179, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653211, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653289, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653358, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653360, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653394, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653396, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653442, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653484, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653487, "dur": 137, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653629, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653667, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653669, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653705, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653707, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653760, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653762, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653797, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653832, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653940, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653975, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409653978, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654012, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654049, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654307, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654342, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654344, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654377, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654408, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654436, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654472, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654473, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654505, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654534, "dur": 203, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654740, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654769, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654876, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654912, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654914, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654955, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409654957, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655039, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655070, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655074, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655107, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655136, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655171, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655173, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655225, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655263, "dur": 162, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655431, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409655469, "dur": 595, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656067, "dur": 42, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656111, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656113, "dur": 167, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656285, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656320, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656321, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656386, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656418, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656449, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656451, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656538, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656569, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656704, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656733, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656767, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656869, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656898, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656926, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656959, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409656962, "dur": 254, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657219, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657223, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657255, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657257, "dur": 241, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657504, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657544, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657545, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657575, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657682, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657709, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657742, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657821, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409657849, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658113, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658115, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658153, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658155, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658192, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658218, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658244, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658278, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658305, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658426, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658450, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658569, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658609, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658610, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658645, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658755, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658794, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658823, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409658851, "dur": 700, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409659553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409659555, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409659599, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409659602, "dur": 50028, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409709637, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409709641, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409709682, "dur": 1595, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409711280, "dur": 7534, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718820, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718825, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718856, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718858, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718904, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718929, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409718975, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719006, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719009, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719132, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719156, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719239, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719266, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719379, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719407, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719550, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719574, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719598, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409719622, "dur": 594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409720220, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409720252, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409720255, "dur": 993, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721251, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721286, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721288, "dur": 245, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721537, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721568, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721571, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721610, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721641, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721643, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721772, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721803, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721860, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721890, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721964, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721995, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409721997, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722185, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722210, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722212, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722304, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722330, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722334, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722736, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409722765, "dur": 940, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723709, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723740, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723742, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723911, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723913, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723944, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409723946, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724001, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724030, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724033, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724067, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724099, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724101, "dur": 198, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724302, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724330, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724359, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724648, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724677, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724679, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724767, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724801, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409724804, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725066, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725096, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725171, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725212, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725215, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725326, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725365, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725368, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725398, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725400, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725437, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725439, "dur": 330, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725774, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725812, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725814, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725843, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725845, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725885, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725953, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725983, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409725985, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726011, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726013, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726041, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726071, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726098, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726121, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726123, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726147, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726171, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726173, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726197, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726235, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726258, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726283, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726284, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726380, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726403, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726404, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726451, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726480, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726481, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726509, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726512, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726537, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726539, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726571, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726573, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726605, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726607, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726641, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726643, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726671, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726703, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726705, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726738, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726740, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726763, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726765, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726789, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726791, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726824, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726827, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726860, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726862, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726888, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726890, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726924, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726927, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726956, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726958, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409726985, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727012, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727014, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727044, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727047, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727082, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727106, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727108, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727134, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727136, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727166, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727168, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727205, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727207, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727244, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727274, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727276, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727314, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727316, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727341, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727343, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727416, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727446, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727447, "dur": 89, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727541, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727572, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727573, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727616, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727617, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727649, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727679, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727681, "dur": 289, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409727974, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409728009, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409728011, "dur": 92978, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409821000, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409821004, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409821052, "dur": 22, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409821076, "dur": 13894, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409834980, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409834985, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409835054, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409835056, "dur": 19627, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854689, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854694, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854740, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854744, "dur": 104, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854854, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854899, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409854902, "dur": 75276, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409930189, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409930194, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409930240, "dur": 27, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409930268, "dur": 2620, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409932893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409932895, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409932929, "dur": 17, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409932948, "dur": 17215, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409950173, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409950179, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409950239, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409950244, "dur": 1450, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409951699, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409951701, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409951749, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409951776, "dur": 24, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409951803, "dur": 8575, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960388, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960392, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960436, "dur": 459, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960900, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960932, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409960935, "dur": 532, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409961470, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409961515, "dur": 22, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409961539, "dur": 525, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409962068, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409962101, "dur": 318, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2724, "tid": 12884901888, "ts": 1749719409962422, "dur": 7843, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2724, "tid": 38, "ts": 1749719409986580, "dur": 1768, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2724, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2724, "tid": 8589934592, "ts": 1749719409604244, "dur": 105109, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2724, "tid": 8589934592, "ts": 1749719409709356, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2724, "tid": 8589934592, "ts": 1749719409709362, "dur": 1035, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2724, "tid": 38, "ts": 1749719409988350, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2724, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2724, "tid": 4294967296, "ts": 1749719409586181, "dur": 385286, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2724, "tid": 4294967296, "ts": 1749719409590071, "dur": 8500, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2724, "tid": 4294967296, "ts": 1749719409971543, "dur": 4564, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2724, "tid": 4294967296, "ts": 1749719409974328, "dur": 87, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2724, "tid": 4294967296, "ts": 1749719409976184, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2724, "tid": 38, "ts": 1749719409988358, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749719409611746, "dur": 1620, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409613377, "dur": 900, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409614396, "dur": 72, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749719409614469, "dur": 595, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409616317, "dur": 696, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749719409618066, "dur": 1284, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749719409615086, "dur": 24405, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409639506, "dur": 322110, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409961617, "dur": 200, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409962069, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409962142, "dur": 1023, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749719409615144, "dur": 24376, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409639550, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409639631, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409640218, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68DB32F33070957F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409640343, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409640630, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409640800, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409640947, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409641063, "dur": 7648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409648798, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409648983, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409649070, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409650585, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409650715, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409651188, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409651344, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409651401, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409652133, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409652377, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409652465, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409652621, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409652687, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409653430, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409653563, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409653761, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409654029, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409654609, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409655046, "dur": 1778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409656825, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409657008, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409657611, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409657798, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409658235, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409658359, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409658685, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749719409658832, "dur": 57651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409716486, "dur": 2324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409718811, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409718872, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409721160, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409721327, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409723926, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409723989, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749719409726686, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409726920, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409727295, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749719409727644, "dur": 233985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409615195, "dur": 24356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409639559, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409639760, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409639847, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_6E9570E2A3C5474A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409640032, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409640205, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409640270, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409640338, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409640519, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409641069, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409641390, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409641637, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749719409641704, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409641846, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749719409641978, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409642234, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749719409642430, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749719409642615, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409643352, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409644074, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409644308, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409644570, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409644819, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409645033, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409645737, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409645991, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409646190, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409646417, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409646630, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409646859, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409647128, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409647496, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409647761, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409648127, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409648575, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409649027, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409649242, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409649584, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409650555, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409651193, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409652403, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409652489, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409652766, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409653388, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409653546, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409653742, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409654315, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409654476, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409654577, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409655128, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749719409655304, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409656392, "dur": 60022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409716416, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409718820, "dur": 858, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409719705, "dur": 2600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409722305, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749719409722391, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409725143, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749719409727681, "dur": 233951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409615352, "dur": 24270, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409639636, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409640030, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B4A1BF472DE35F91.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409640129, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5E49D6BEFCDFF46D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409640223, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_98C9D7CFA3055094.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409640357, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409640539, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0B3C92E79B34D00C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409640669, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_31DA0AEF6717D1CD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409640935, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749719409641045, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409641296, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749719409641964, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409642184, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749719409642327, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749719409642536, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749719409642632, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409643265, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409644025, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409644315, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409644605, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409644811, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409645044, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409645605, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409645822, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409646051, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409646274, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409646509, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409646756, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409646980, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409647281, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409647500, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409647733, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409648018, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409648483, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409648804, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409649149, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409649275, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409649747, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409650521, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409651174, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409651344, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409651448, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409652293, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409652396, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409652471, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409652867, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409653509, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409653761, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409654075, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409655026, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409655222, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409655788, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409655900, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409656500, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749719409656612, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409657013, "dur": 59396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409716412, "dur": 2387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409718801, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409718904, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409721376, "dur": 879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749719409722265, "dur": 2541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409724852, "dur": 3164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749719409728067, "dur": 233571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409615182, "dur": 24357, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409639555, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409639633, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409640011, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409640197, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_84BF5D8C55DA5D3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409640264, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409640380, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409640518, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409640706, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6EBD506B2644EFF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409640764, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409640890, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409641053, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409641112, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409641162, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749719409641236, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409641287, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749719409641560, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749719409642476, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749719409642566, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409643160, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409643776, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409644011, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409644219, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409644541, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409644748, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409644986, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409645670, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409645979, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409646194, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409646498, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409646749, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409646957, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409647283, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409647533, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409647849, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409648118, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409648443, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409648858, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409649357, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409649927, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409650524, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409651205, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409651381, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409651477, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409652119, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409652374, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409652755, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749719409653091, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409653174, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409653541, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409653802, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409654419, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409654574, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409655021, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749719409655145, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409655544, "dur": 60855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409716400, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409718818, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409718891, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409721354, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409721685, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409724081, "dur": 3166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749719409727247, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749719409727507, "dur": 234106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409615284, "dur": 24294, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409639590, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409639878, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409640202, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409640382, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409640657, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F4B95EF39F1A8010.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409640800, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409640895, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409640953, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749719409641075, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409641144, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749719409641232, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409641686, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749719409642003, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409642175, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749719409642503, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5334484173090267189.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749719409642599, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5334484173090267189.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749719409642675, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409643378, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409644032, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409644233, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409644611, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409644843, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409645096, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409645650, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409645923, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409646197, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409646417, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409646676, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409646901, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409647217, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409647464, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409647738, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409648055, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409648420, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409648677, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409649067, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409649211, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409649425, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409649924, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409650525, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409651378, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409651565, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409651631, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409652210, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409652438, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409652619, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409652784, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409653084, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409653442, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409653549, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409653763, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409654406, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409654818, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409655031, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409656506, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409656630, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409657789, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409657905, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409658533, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749719409658641, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409658932, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409660254, "dur": 160812, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409825677, "dur": 9158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749719409825307, "dur": 9609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409834918, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409834993, "dur": 16496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749719409834990, "dur": 18070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409854383, "dur": 178, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749719409855248, "dur": 77729, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749719409951772, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749719409951759, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749719409951874, "dur": 9773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409615231, "dur": 24333, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409639573, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409639727, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409639828, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3DE1FE7E9BAAF7A9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409639884, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409640086, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409640327, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409640394, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_CB49B96AA053BB61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409640746, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409641036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409641098, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409641235, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409641566, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749719409641828, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1749719409641893, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409642306, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749719409642560, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409642772, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409643520, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409643801, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409644003, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409644202, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409644456, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409644758, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409644999, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409645686, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409645933, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409646156, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409646391, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409646608, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409646862, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409647072, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409647391, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409647624, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409647846, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409648085, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409648391, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409648678, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409648992, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409649360, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409649987, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409650533, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409651213, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409651414, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749719409652213, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409652394, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409652778, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749719409653675, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409653887, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409654593, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409655084, "dur": 3608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409658693, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749719409658884, "dur": 58072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409716957, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749719409719254, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409719465, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749719409721717, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409721940, "dur": 3118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749719409725059, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409725500, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409725904, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409726367, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409726488, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409726863, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409727089, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409727324, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749719409727744, "dur": 233912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409615339, "dur": 24253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409639602, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409639917, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409640195, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409640361, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409640510, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_36273396CDE3C103.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409640895, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409640971, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409641069, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409641221, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749719409641501, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749719409641834, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749719409642079, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1749719409642312, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16372163617230684214.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749719409642501, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749719409642648, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409643219, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409644118, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409644439, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409644705, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409644942, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409645561, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409645773, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409646087, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409646298, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409646522, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409646789, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409646990, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409647267, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409647485, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409647755, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409647982, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409648341, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409648942, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409649292, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409649628, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409650520, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409651176, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409651343, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409651450, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409651510, "dur": 1818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409653328, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409653408, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409653548, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409653769, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409655118, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409655264, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409655519, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409656820, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749719409656954, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409657325, "dur": 59416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409716743, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409719318, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409719635, "dur": 3913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409723549, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409723784, "dur": 3401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749719409727186, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409727328, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749719409728079, "dur": 233560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409615389, "dur": 24252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409639652, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749719409639728, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409639867, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_DE30F0D9293C6825.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749719409640146, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409640220, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_233FB08ADCB5306F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749719409640363, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_42313A2B468B0196.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749719409640747, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409640917, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409641216, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749719409641392, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409641529, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749719409641728, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409641981, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409642299, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749719409642355, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409642556, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409643125, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409643888, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409644197, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409644578, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409644801, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409645125, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409645771, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409646048, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409646262, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409646467, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409646774, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409646986, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409647262, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409647583, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409647845, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409648059, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409648291, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409648540, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409648914, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409649636, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409650531, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409651240, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749719409651484, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409652690, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409652786, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409653131, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409653549, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749719409653766, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409654381, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409654569, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409655027, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409655125, "dur": 61282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749719409716409, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409718854, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409718980, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409721642, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409724390, "dur": 2960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749719409727408, "dur": 234209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409615425, "dur": 24235, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409639673, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409639755, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409639877, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409640180, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_94383F50AE2F3B27.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409640252, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409640362, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409640521, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409640960, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409641234, "dur": 7216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409648453, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409648672, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409649160, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409649369, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409649944, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409650539, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409651211, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409651363, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409651462, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409652232, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409652503, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409653411, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409653556, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409653790, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409654572, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409655026, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749719409655152, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409655688, "dur": 60735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409716434, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409719216, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409719331, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409721852, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409724298, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749719409724725, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749719409727641, "dur": 234018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409615480, "dur": 24200, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409639689, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409639756, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409639927, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_13ED6B36C70FAB0A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409640108, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409640161, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409640226, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409640349, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409640638, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409640797, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_22FDE6BD161A4064.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409640903, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409641030, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409641193, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749719409641296, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749719409641891, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409641957, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749719409642049, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409642104, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749719409642389, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409642441, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749719409642577, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409643140, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409644129, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409644381, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409644650, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409644875, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409645095, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409645598, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409645815, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409646075, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409646297, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409646504, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409646734, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409646953, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409647280, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409647509, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409647752, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409648285, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409648799, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409649256, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409649582, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409650507, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409651189, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409651340, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409651421, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409652060, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409652274, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409652529, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409652822, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409653384, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409653563, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749719409653708, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409654395, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409654467, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409654575, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409655133, "dur": 61264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409716398, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409718996, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409719479, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409721953, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409722066, "dur": 2515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409724582, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749719409725256, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749719409727735, "dur": 233899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409615532, "dur": 24166, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409639712, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409639835, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_06983A7F9FE1EEBF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409639890, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409640064, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DDBE5B8EC557ADE5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409640190, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409640311, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9C249CA42F8C791A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409640367, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409640536, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409640603, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409640678, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409640892, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409640968, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409641144, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749719409641572, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749719409641815, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749719409642116, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409642179, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749719409642282, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749719409642552, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409643228, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409643810, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409644024, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409644222, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409644535, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409644759, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409645013, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409645714, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409645973, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409646257, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409646481, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409646718, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409646979, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409647200, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409647427, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409647689, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409647904, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409648111, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409648428, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409648946, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409649225, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409649587, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409650510, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409651180, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409651343, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409651425, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749719409652296, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409652548, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749719409653156, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409653393, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749719409653511, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749719409654112, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409654589, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409655095, "dur": 55383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409711869, "dur": 326, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 11, "ts": 1749719409712196, "dur": 1285, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 11, "ts": 1749719409713481, "dur": 84, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 11, "ts": 1749719409710479, "dur": 3094, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409713574, "dur": 3911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409717487, "dur": 2553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749719409720041, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409720298, "dur": 2448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749719409722747, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409722813, "dur": 2553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749719409725410, "dur": 762, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409726524, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749719409726690, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749719409727217, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409727310, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749719409727362, "dur": 224404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749719409951798, "dur": 8586, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749719409951771, "dur": 8615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749719409960433, "dur": 1188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409615566, "dur": 24149, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409639727, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409639895, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409639993, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4D5EF4ECE256D09B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409640110, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409640226, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409640351, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409640773, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409640870, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409641033, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749719409641164, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749719409641394, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409641575, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749719409641696, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749719409641784, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749719409642080, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749719409642580, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409643117, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409643703, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409644317, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409644566, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409644773, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409645018, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409645171, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409645776, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409646044, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409646250, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409646446, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409646744, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409646994, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409647445, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409647834, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409648034, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409648404, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409648891, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409649156, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409649371, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409649993, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409650589, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409651370, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409651598, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409652175, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409652868, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409653177, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409653543, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409653696, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409653758, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749719409654018, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409654566, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1749719409655011, "dur": 114, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409655528, "dur": 54192, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1749719409716419, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409718870, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409721278, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409721352, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409723825, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409724150, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749719409726772, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409726890, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409726999, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409727236, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749719409727297, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749719409727698, "dur": 233929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409615603, "dur": 24133, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409639758, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409639861, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_774893DE4F1F8941.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409640291, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409640346, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409640566, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_196389E2854E5BDF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409640823, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749719409640978, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749719409641034, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409641123, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749719409641255, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749719409641405, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409641465, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749719409641714, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409641849, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749719409641964, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409642256, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749719409642594, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749719409642659, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409643384, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409643978, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409644177, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409644447, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409644726, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409645026, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409645749, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409646073, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409646295, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409646534, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409646873, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409647163, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409647405, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409647641, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409647877, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409648261, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409648704, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409649261, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409649715, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409650554, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409651230, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409651380, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409651456, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409651768, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749719409652531, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749719409653123, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409653252, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409653546, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409653796, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409654570, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409655022, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749719409655155, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749719409655553, "dur": 60842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409716397, "dur": 2777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749719409719229, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749719409721634, "dur": 4130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749719409725764, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409725936, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749719409726036, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409726144, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409726579, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409727077, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409727256, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749719409727421, "dur": 234191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409615639, "dur": 24125, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409639775, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409639861, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409640034, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409640373, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409640770, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409641028, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409641293, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749719409641572, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749719409642143, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749719409642431, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749719409642597, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749719409642669, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409643356, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409644062, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409644291, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409644624, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409644822, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409645111, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409645711, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409646006, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409646210, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409646408, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409646713, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409646925, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409647191, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409647803, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409648001, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409648458, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409648962, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409649196, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409649428, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409649646, "dur": 881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409650528, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409651189, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409651405, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409652165, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409652511, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409652675, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409653561, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409653728, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409653846, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409654571, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409655024, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409655167, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409655299, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409655804, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409655978, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409656736, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409656837, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409657017, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409657616, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749719409657793, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409658309, "dur": 58111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409716422, "dur": 2778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409719201, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409719326, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409721634, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409721693, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409724086, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409724165, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409726783, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409726945, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749719409727233, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749719409727341, "dur": 97977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409825352, "dur": 26172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749719409825326, "dur": 27734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409854714, "dur": 197, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749719409855374, "dur": 74854, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749719409949984, "dur": 10968, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749719409949972, "dur": 10981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749719409960972, "dur": 583, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749719409615666, "dur": 24114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409639789, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409639889, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1FDEA3861C839C9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409640021, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B4FC9B590A58509F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409640122, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_F66768BDA37A5632.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409640370, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409640533, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_5D725690D6CA2AA1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409640617, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_5D725690D6CA2AA1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409640771, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749719409641040, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409641197, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409641404, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409641723, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409641775, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749719409641912, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409642170, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749719409642331, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749719409642550, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409643066, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409643715, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409643924, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409644125, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409644360, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409644659, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409644909, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409645151, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409645759, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409646031, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409646254, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409646474, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409646698, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409646903, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409647156, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409647666, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409647878, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409648087, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409648273, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409648614, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409649169, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409649604, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409650545, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409651209, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409651375, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409651476, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749719409653212, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409653276, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409653381, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749719409653560, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749719409654066, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409654450, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409654579, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409655108, "dur": 58472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409713580, "dur": 2823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409716406, "dur": 2578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749719409718985, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409719063, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749719409721410, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409721618, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749719409724069, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409724145, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749719409726995, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409727265, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749719409727520, "dur": 234090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409615706, "dur": 24088, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409639795, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409639895, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409639996, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409640080, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409640192, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409640371, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409641067, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409641248, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749719409641329, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749719409641479, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749719409641670, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749719409641804, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749719409641937, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409642512, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749719409642623, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409642796, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409643369, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409644019, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409644227, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409644533, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409644786, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409645618, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409645857, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409646162, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409646375, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409646587, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409646833, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409647071, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409647314, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409647530, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409647778, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409648036, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409648367, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409648903, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409649295, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409650035, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409650516, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409651178, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409651345, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409651408, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409652118, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409652479, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409652657, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409652745, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409653400, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409653457, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409653549, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409653764, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749719409654050, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409654590, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409655070, "dur": 2547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409657617, "dur": 58811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409716430, "dur": 2724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409719212, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409721695, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409724351, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409724414, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749719409727225, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409727356, "dur": 222622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749719409950005, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749719409949980, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749719409950214, "dur": 1559, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749719409951777, "dur": 9891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749719409968505, "dur": 1417, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2724, "tid": 38, "ts": 1749719409988849, "dur": 2804, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2724, "tid": 38, "ts": 1749719409991691, "dur": 1216, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2724, "tid": 38, "ts": 1749719409984274, "dur": 9545, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}