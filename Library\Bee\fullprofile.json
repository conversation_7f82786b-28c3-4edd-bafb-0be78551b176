{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 7120, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 7120, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 7120, "tid": 46, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 7120, "tid": 46, "ts": 1749034107248458, "dur": 905, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 7120, "tid": 46, "ts": 1749034107253551, "dur": 637, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 7120, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 7120, "tid": 1, "ts": 1749034104125978, "dur": 7414, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7120, "tid": 1, "ts": 1749034104133398, "dur": 88391, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7120, "tid": 1, "ts": 1749034104221800, "dur": 88315, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 7120, "tid": 46, "ts": 1749034107254193, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 7120, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104124019, "dur": 17746, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104141766, "dur": 3097155, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104142790, "dur": 3608, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104146403, "dur": 1848, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148254, "dur": 333, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148591, "dur": 11, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148602, "dur": 57, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148663, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148665, "dur": 35, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148702, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148704, "dur": 38, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148746, "dur": 93, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148869, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148907, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148909, "dur": 26, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148938, "dur": 38, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148979, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104148980, "dur": 30, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149014, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149015, "dur": 31, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149050, "dur": 36, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149089, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149115, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149142, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149170, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149199, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149225, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149252, "dur": 27, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149281, "dur": 29, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149311, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149314, "dur": 27, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149343, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149344, "dur": 23, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149369, "dur": 28, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149400, "dur": 23, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149426, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149460, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149495, "dur": 26, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149524, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149553, "dur": 28, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149583, "dur": 26, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149612, "dur": 35, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149649, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149680, "dur": 28, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149727, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149729, "dur": 30, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149760, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149762, "dur": 27, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149792, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149819, "dur": 46, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149870, "dur": 58, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149931, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149942, "dur": 34, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149978, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104149980, "dur": 28, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150011, "dur": 33, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150047, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150082, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150109, "dur": 27, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150138, "dur": 33, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150174, "dur": 34, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150211, "dur": 26, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150238, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150240, "dur": 27, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150270, "dur": 24, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150296, "dur": 30, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150330, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150366, "dur": 40, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150409, "dur": 29, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150440, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150441, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150469, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150501, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150561, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150563, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150600, "dur": 1, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150602, "dur": 26, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150630, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150655, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150687, "dur": 24, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150714, "dur": 25, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150742, "dur": 28, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150772, "dur": 8, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150782, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150817, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150849, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150877, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150905, "dur": 29, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150937, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150965, "dur": 26, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104150994, "dur": 27, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151023, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151024, "dur": 24, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151050, "dur": 28, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151081, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151104, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151105, "dur": 35, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151143, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151174, "dur": 24, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151201, "dur": 26, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151229, "dur": 24, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151256, "dur": 24, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151282, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151306, "dur": 23, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151332, "dur": 33, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151368, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151370, "dur": 34, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151406, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151408, "dur": 31, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151441, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151443, "dur": 39, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151485, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151517, "dur": 25, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151544, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151574, "dur": 28, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151604, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151632, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151636, "dur": 124, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151762, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151790, "dur": 27, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151820, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151847, "dur": 27, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151876, "dur": 24, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151902, "dur": 34, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151940, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151968, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151969, "dur": 23, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104151994, "dur": 27, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152023, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152024, "dur": 25, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152051, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152078, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152103, "dur": 26, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152132, "dur": 29, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152163, "dur": 24, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152190, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152218, "dur": 22, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152243, "dur": 28, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152273, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152299, "dur": 22, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152323, "dur": 30, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152355, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152356, "dur": 26, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152384, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152413, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152440, "dur": 28, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152471, "dur": 22, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152495, "dur": 28, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152525, "dur": 24, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152552, "dur": 25, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152579, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152607, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152642, "dur": 23, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152667, "dur": 27, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152697, "dur": 23, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152722, "dur": 24, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152749, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152781, "dur": 23, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152806, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152832, "dur": 29, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152863, "dur": 29, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152895, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152896, "dur": 34, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152933, "dur": 29, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152964, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152965, "dur": 25, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104152993, "dur": 23, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153018, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153046, "dur": 28, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153076, "dur": 29, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153108, "dur": 24, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153135, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153165, "dur": 29, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153196, "dur": 23, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153222, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153249, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153281, "dur": 24, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153307, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153333, "dur": 25, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153360, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153362, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153389, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153416, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153446, "dur": 26, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153474, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153501, "dur": 70, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153574, "dur": 37, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153613, "dur": 1, "ph": "X", "name": "ProcessMessages 1635", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153615, "dur": 25, "ph": "X", "name": "ReadAsync 1635", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153642, "dur": 24, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153669, "dur": 24, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153695, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153720, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153747, "dur": 26, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153776, "dur": 25, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153803, "dur": 27, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153836, "dur": 26, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153864, "dur": 26, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153892, "dur": 22, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153917, "dur": 30, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153950, "dur": 29, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104153983, "dur": 61, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154045, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154046, "dur": 33, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154081, "dur": 1, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154083, "dur": 61, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154146, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154147, "dur": 32, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154181, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154183, "dur": 25, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154210, "dur": 26, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154238, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154240, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154267, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154269, "dur": 24, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154295, "dur": 52, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154348, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154350, "dur": 21, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154373, "dur": 1, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154375, "dur": 52, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154428, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154430, "dur": 28, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154461, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154488, "dur": 25, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154516, "dur": 25, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154543, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154545, "dur": 29, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154577, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154578, "dur": 24, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154604, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154629, "dur": 41, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154673, "dur": 28, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154704, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154731, "dur": 26, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154759, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154788, "dur": 53, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154843, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154846, "dur": 43, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154892, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154894, "dur": 27, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154924, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154960, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104154962, "dur": 46, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155012, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155014, "dur": 31, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155046, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155047, "dur": 24, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155073, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155103, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155106, "dur": 26, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155134, "dur": 26, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155162, "dur": 29, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155194, "dur": 45, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155240, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155242, "dur": 33, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155278, "dur": 29, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155310, "dur": 25, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155338, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155364, "dur": 31, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155397, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155399, "dur": 52, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155454, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155475, "dur": 33, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155510, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155535, "dur": 25, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155563, "dur": 31, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155633, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155635, "dur": 37, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155725, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155728, "dur": 94, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104155824, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156178, "dur": 72, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156251, "dur": 4, "ph": "X", "name": "ProcessMessages 8399", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156256, "dur": 30, "ph": "X", "name": "ReadAsync 8399", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156291, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156323, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156325, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156357, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156394, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156395, "dur": 32, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156429, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156456, "dur": 328, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156786, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156788, "dur": 51, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156841, "dur": 3, "ph": "X", "name": "ProcessMessages 3746", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156845, "dur": 25, "ph": "X", "name": "ReadAsync 3746", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156872, "dur": 29, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156904, "dur": 27, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156933, "dur": 28, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156963, "dur": 27, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156992, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104156994, "dur": 37, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157039, "dur": 32, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157074, "dur": 32, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157107, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157109, "dur": 32, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157144, "dur": 26, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157174, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157176, "dur": 34, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157232, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157234, "dur": 31, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157272, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157274, "dur": 28, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157310, "dur": 23, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157336, "dur": 26, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157365, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157393, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157418, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157455, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157457, "dur": 58, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157517, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157519, "dur": 29, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157550, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157552, "dur": 32, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157586, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157587, "dur": 23, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157613, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157639, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157643, "dur": 34, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157680, "dur": 28, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157711, "dur": 25, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157738, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157739, "dur": 32, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157774, "dur": 30, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157807, "dur": 36, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157846, "dur": 24, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157873, "dur": 22, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157896, "dur": 27, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157926, "dur": 36, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157966, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104157998, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158023, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158051, "dur": 107, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158161, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158191, "dur": 28, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158221, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158222, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158252, "dur": 25, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158279, "dur": 26, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158309, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158337, "dur": 23, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158362, "dur": 23, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158388, "dur": 63, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158453, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158484, "dur": 25, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158512, "dur": 84, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158600, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158637, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158638, "dur": 26, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158666, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158668, "dur": 74, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158744, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158770, "dur": 32, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158804, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158806, "dur": 29, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158837, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158839, "dur": 63, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158904, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158933, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104158959, "dur": 83, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159043, "dur": 40, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159086, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159120, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159122, "dur": 46, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159171, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159197, "dur": 34, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159233, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159234, "dur": 27, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159270, "dur": 59, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159331, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159363, "dur": 25, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159389, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159392, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159420, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159479, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159481, "dur": 40, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159523, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159550, "dur": 30, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159582, "dur": 23, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159609, "dur": 73, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159685, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159716, "dur": 27, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159747, "dur": 47, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159797, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159870, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159903, "dur": 26, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104159932, "dur": 77, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160012, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160039, "dur": 23, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160064, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160091, "dur": 87, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160186, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160272, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160327, "dur": 43, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160373, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160375, "dur": 30, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160408, "dur": 76, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160488, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160585, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160589, "dur": 29, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160644, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160691, "dur": 29, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160721, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160722, "dur": 53, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160779, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160808, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160809, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160839, "dur": 121, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104160964, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161000, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161002, "dur": 28, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161077, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161079, "dur": 36, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161117, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161119, "dur": 36, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161269, "dur": 46, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161317, "dur": 128, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161448, "dur": 2, "ph": "X", "name": "ProcessMessages 1270", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161450, "dur": 32, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161485, "dur": 28, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161516, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161517, "dur": 33, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161553, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161577, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161579, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161665, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161695, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161725, "dur": 27, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161755, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161779, "dur": 27, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161808, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161810, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161838, "dur": 27, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161868, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161892, "dur": 88, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104161983, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162018, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162044, "dur": 24, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162071, "dur": 84, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162158, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162186, "dur": 32, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162221, "dur": 39, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162263, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162292, "dur": 26, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162321, "dur": 24, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162347, "dur": 17, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162369, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162398, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162469, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162501, "dur": 55, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162559, "dur": 25, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162586, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162588, "dur": 58, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162648, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162716, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162718, "dur": 35, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162755, "dur": 1, "ph": "X", "name": "ProcessMessages 1637", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162756, "dur": 21, "ph": "X", "name": "ReadAsync 1637", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162781, "dur": 1, "ph": "X", "name": "ProcessMessages 33", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162782, "dur": 50, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162836, "dur": 51, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162890, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162962, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162994, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104162996, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163023, "dur": 84, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163110, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163139, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163141, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163172, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163174, "dur": 24, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163200, "dur": 45, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163246, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163277, "dur": 26, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163306, "dur": 25, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163334, "dur": 24, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163361, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163386, "dur": 16, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163403, "dur": 26, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163430, "dur": 1, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163432, "dur": 46, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163481, "dur": 25, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163508, "dur": 101, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163613, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163651, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163652, "dur": 34, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163688, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163689, "dur": 29, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163721, "dur": 72, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163797, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163829, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163830, "dur": 30, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163864, "dur": 83, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163950, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104163974, "dur": 25, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164001, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164003, "dur": 32, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164037, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164038, "dur": 30, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164071, "dur": 35, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164110, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164111, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164145, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164147, "dur": 26, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164176, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164204, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164206, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164291, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164320, "dur": 30, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164354, "dur": 24, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164381, "dur": 82, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164466, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164496, "dur": 40, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164538, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164544, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164578, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164579, "dur": 27, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164609, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164610, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164643, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164714, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164743, "dur": 57, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164802, "dur": 37, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164842, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164843, "dur": 62, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164909, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164958, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104164963, "dur": 35, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165000, "dur": 37, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165039, "dur": 28, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165069, "dur": 47, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165156, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165159, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165199, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165202, "dur": 27, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165231, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165233, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165305, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165338, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165340, "dur": 27, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165370, "dur": 77, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165718, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165822, "dur": 2, "ph": "X", "name": "ProcessMessages 2660", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165825, "dur": 34, "ph": "X", "name": "ReadAsync 2660", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165944, "dur": 1, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165947, "dur": 34, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165983, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104165984, "dur": 27, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166016, "dur": 305, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166325, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166365, "dur": 2, "ph": "X", "name": "ProcessMessages 2282", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166368, "dur": 264, "ph": "X", "name": "ReadAsync 2282", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166634, "dur": 1, "ph": "X", "name": "ProcessMessages 2037", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166636, "dur": 25, "ph": "X", "name": "ReadAsync 2037", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166664, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104166739, "dur": 270, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167012, "dur": 40, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167054, "dur": 1, "ph": "X", "name": "ProcessMessages 2021", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167056, "dur": 75, "ph": "X", "name": "ReadAsync 2021", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167133, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167134, "dur": 26, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167163, "dur": 55, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167222, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167279, "dur": 309, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167590, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167591, "dur": 37, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167629, "dur": 1, "ph": "X", "name": "ProcessMessages 2376", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167632, "dur": 51, "ph": "X", "name": "ReadAsync 2376", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104167685, "dur": 505, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168193, "dur": 1, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168207, "dur": 63, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168272, "dur": 3, "ph": "X", "name": "ProcessMessages 4053", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168309, "dur": 39, "ph": "X", "name": "ReadAsync 4053", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168350, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168352, "dur": 29, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168383, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168634, "dur": 134, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168771, "dur": 2, "ph": "X", "name": "ProcessMessages 2814", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168775, "dur": 35, "ph": "X", "name": "ReadAsync 2814", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168814, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168817, "dur": 21, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168841, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104168907, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169311, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169313, "dur": 46, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169372, "dur": 353, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169847, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169924, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169965, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104169967, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170024, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170028, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170070, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170073, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170120, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170123, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170170, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170172, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170253, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170257, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170292, "dur": 48, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170343, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170394, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170399, "dur": 40, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170454, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170457, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170504, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170508, "dur": 40, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170551, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170554, "dur": 78, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170634, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170676, "dur": 50, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170730, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170735, "dur": 53, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170791, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104170794, "dur": 28, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171169, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171175, "dur": 76, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171255, "dur": 13, "ph": "X", "name": "ProcessMessages 1760", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171273, "dur": 46, "ph": "X", "name": "ReadAsync 1760", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171646, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171650, "dur": 64, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171717, "dur": 10, "ph": "X", "name": "ProcessMessages 1428", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171730, "dur": 99, "ph": "X", "name": "ReadAsync 1428", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171833, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171835, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171886, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171890, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104171998, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172004, "dur": 85, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172093, "dur": 3, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172098, "dur": 69, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172171, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172174, "dur": 26, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172205, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172289, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172330, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104172333, "dur": 7683, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180025, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180029, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180092, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180095, "dur": 597, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180698, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180742, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104180744, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104181131, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104181133, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104181187, "dur": 1159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182352, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182458, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182460, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182491, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182562, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182843, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182886, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104182888, "dur": 266, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183214, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183290, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183293, "dur": 124, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183420, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183445, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183447, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183525, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183559, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183561, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183641, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183677, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183807, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183811, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183845, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183847, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183900, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183902, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183934, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104183936, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184052, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184090, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184092, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184212, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184215, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184250, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184252, "dur": 77, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184331, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184334, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184368, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184420, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184458, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184460, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184564, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184566, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184596, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184598, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184700, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104184701, "dur": 364, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185070, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185110, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185113, "dur": 44, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185160, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185162, "dur": 324, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185489, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185493, "dur": 29, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185526, "dur": 76, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185604, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185606, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185638, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185667, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185768, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185770, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185845, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185847, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185886, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185921, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104185955, "dur": 310, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104186269, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104186300, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104186302, "dur": 520, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104186829, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104186923, "dur": 6, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104186930, "dur": 95, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187029, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187031, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187066, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187072, "dur": 77, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187153, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187157, "dur": 314, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187477, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187479, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104187523, "dur": 641, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188167, "dur": 85, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188255, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188257, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188292, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188329, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188331, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188449, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188493, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188495, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188802, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188832, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188834, "dur": 92, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188933, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188971, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104188997, "dur": 435, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189439, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189474, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189476, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189517, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189519, "dur": 318, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189842, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189872, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189874, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189927, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189929, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104189964, "dur": 289, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104190258, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104190286, "dur": 382, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104190922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104190924, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104190960, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104190964, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104191009, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104191094, "dur": 749, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104191849, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104191881, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104191888, "dur": 51890, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104243786, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104243790, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104243833, "dur": 3113, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104246951, "dur": 4657, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251613, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251616, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251703, "dur": 51, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251757, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251799, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251930, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104251932, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252031, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252034, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252071, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252160, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252252, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252255, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252289, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252292, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104252356, "dur": 1066, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104253428, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104253462, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104253464, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104253547, "dur": 199, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104253778, "dur": 30, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104253809, "dur": 419, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254232, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254234, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254270, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254272, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254336, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254338, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254516, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254550, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254552, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254800, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254898, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254900, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104254932, "dur": 511, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104255448, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104255483, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104255485, "dur": 645, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256134, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256213, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256216, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256276, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256304, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256306, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256599, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256629, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256694, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104256695, "dur": 655, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257355, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257483, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257486, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257528, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257530, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257667, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257699, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257701, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257812, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104257844, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104258141, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104258173, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104258175, "dur": 458, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104258638, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104258699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104258700, "dur": 303, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259007, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259036, "dur": 615, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259656, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259699, "dur": 4, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259734, "dur": 32, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259770, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259772, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259853, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259855, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259899, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259901, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259984, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104259986, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260023, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260025, "dur": 470, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260545, "dur": 5, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260552, "dur": 63, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260619, "dur": 220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260841, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260874, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260911, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104260913, "dur": 262, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104261179, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034104261217, "dur": 1494789, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105756015, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105756020, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105756046, "dur": 19, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105756066, "dur": 9440, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105765518, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105765523, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105765579, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105765583, "dur": 14917, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780509, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780513, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780564, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780568, "dur": 75, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780649, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780688, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034105780690, "dur": 1425577, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107206277, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107206282, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107206335, "dur": 30, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107206367, "dur": 23564, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107229940, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107229944, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107229994, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107229998, "dur": 1375, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107231381, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107231421, "dur": 40, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107231463, "dur": 545, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107232011, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107232013, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107232044, "dur": 303, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 7120, "tid": 12884901888, "ts": 1749034107232349, "dur": 6507, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 7120, "tid": 46, "ts": 1749034107254206, "dur": 996, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 7120, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 7120, "tid": 8589934592, "ts": 1749034104111474, "dur": 198716, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 7120, "tid": 8589934592, "ts": 1749034104310194, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 7120, "tid": 8589934592, "ts": 1749034104310199, "dur": 1430, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 7120, "tid": 46, "ts": 1749034107255204, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 7120, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 7120, "tid": 4294967296, "ts": 1749034104085288, "dur": 3154877, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 7120, "tid": 4294967296, "ts": 1749034104094009, "dur": 10163, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 7120, "tid": 4294967296, "ts": 1749034107240227, "dur": 5047, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 7120, "tid": 4294967296, "ts": 1749034107243243, "dur": 91, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 7120, "tid": 4294967296, "ts": 1749034107245452, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 7120, "tid": 46, "ts": 1749034107255209, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749034104138452, "dur": 1360, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034104139822, "dur": 838, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034104140779, "dur": 72, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749034104140851, "dur": 473, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034104142534, "dur": 2510, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749034104146425, "dur": 1844, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749034104155900, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749034104156154, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749034104141346, "dur": 27267, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034104168628, "dur": 3062511, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034107231140, "dur": 276, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034107231686, "dur": 1022, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749034104141419, "dur": 27223, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104168736, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104169803, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170010, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170129, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170183, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8E726FC19BF52AA2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170349, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170448, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170603, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170665, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749034104170977, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749034104171283, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749034104171494, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749034104171795, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104173432, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Pkcs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749034104171987, "dur": 2400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104174387, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104174659, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104175061, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104175337, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104176028, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104176346, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104176590, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104177005, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104177413, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104177676, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104178018, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104178324, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104178895, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104179159, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104179585, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104180192, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104180611, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104181247, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104182064, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104182601, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104182818, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104183229, "dur": 2101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104185423, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104185626, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104186408, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104186638, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104187230, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104187391, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104188297, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104188468, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104189004, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749034104189172, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104189504, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104190316, "dur": 58676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104248993, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104251298, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104251619, "dur": 2605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104254224, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104254497, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104257047, "dur": 2843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749034104260076, "dur": 51258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104313974, "dur": 397, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1749034104314371, "dur": 5659, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1749034104320030, "dur": 104, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1749034104311335, "dur": 8805, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749034104320141, "dur": 2911035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104141544, "dur": 27170, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104168724, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749034104169903, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749034104169955, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104170175, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_36273396CDE3C103.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749034104170483, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1749034104170676, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749034104170995, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749034104171179, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749034104171288, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749034104171562, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749034104171736, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749034104171814, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104172153, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104173675, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104174002, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104174210, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104174413, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104174654, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104175279, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104175523, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104176134, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104176560, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104176879, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104177199, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104177523, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104177924, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104178458, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104178922, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104179234, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104179643, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104179896, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104180298, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104181007, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104181568, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104182071, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104182599, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749034104182841, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104183701, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104183936, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749034104184200, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104185159, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749034104185323, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104185751, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104185923, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749034104186376, "dur": 201, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104186963, "dur": 56072, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1749034104248995, "dur": 2271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104251268, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104251433, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104253588, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104253662, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104256087, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104256255, "dur": 2293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749034104258549, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104259095, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104259546, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104259672, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104260072, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749034104260588, "dur": 2970576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104141461, "dur": 27214, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104168686, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104169870, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104169985, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9C249CA42F8C791A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104170168, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4A1C632ABDE44C9A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104170352, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104170563, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104170721, "dur": 8891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104179721, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104180067, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104180477, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104180917, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104181544, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104182107, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104182846, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104183008, "dur": 769, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104183782, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104184022, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104184108, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104184954, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104185085, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104185179, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104185427, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104185926, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104186404, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104186687, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104186879, "dur": 1295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104188263, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749034104188368, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104188964, "dur": 1382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104190346, "dur": 58673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104249020, "dur": 4175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104253243, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104255780, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104255980, "dur": 2768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749034104258749, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104258990, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104259083, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104259657, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104259840, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104260070, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749034104260191, "dur": 2970982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104141449, "dur": 27213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104168678, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104168739, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104169950, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104170129, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104170553, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104170643, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749034104170877, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749034104171075, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749034104171288, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749034104171521, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749034104171627, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749034104171720, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749034104171803, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104171963, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104172187, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104173453, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104174883, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104175087, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104175367, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104175952, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104176234, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104176521, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104176751, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104177026, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104177317, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104177616, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104178177, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104178498, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104178815, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104179272, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104179576, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104180121, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104180477, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104181061, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104181492, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104182117, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104182622, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104182931, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104183091, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749034104184551, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104184617, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104184832, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104185023, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749034104185434, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104185956, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104186412, "dur": 3896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104190310, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749034104190474, "dur": 58533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104249009, "dur": 2808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749034104251818, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104251944, "dur": 2329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749034104254274, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104254966, "dur": 3577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749034104258544, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104259004, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104259064, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104259193, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034104259467, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749034104259666, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749034104260096, "dur": 1499618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034105759742, "dur": 17185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749034105759717, "dur": 18731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749034105780135, "dur": 181, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749034105780742, "dur": 1425207, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749034107229412, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749034107229405, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749034107229584, "dur": 1491, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749034107231078, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104141498, "dur": 27191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104168696, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104169998, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104170131, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104170355, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104170515, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749034104170900, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749034104171226, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749034104171729, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749034104171812, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104172197, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104174058, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104174297, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104174617, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104174911, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104175129, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104175403, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104175971, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104176309, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104176581, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104176903, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104177149, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104177417, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104177753, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104178030, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104178379, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104178742, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104179067, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104179536, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104179927, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104180272, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104180787, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104181450, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104182126, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104182626, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104182850, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104183546, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104183918, "dur": 1391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104185362, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104185421, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749034104185601, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104186066, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104186415, "dur": 3899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104190314, "dur": 58698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104249014, "dur": 2308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104251323, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104251397, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104254015, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104254173, "dur": 2750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104256924, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749034104257295, "dur": 2811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749034104260193, "dur": 2970960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104141529, "dur": 27171, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104168708, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104169784, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B4FC9B590A58509F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104170030, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_42313A2B468B0196.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104170156, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104170813, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749034104171125, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749034104171272, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749034104171560, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749034104171638, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749034104171794, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104171956, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104172179, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104173608, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104173928, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104174140, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104174356, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104174708, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104175195, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104175509, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104176110, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104176620, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104176965, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104177153, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104177437, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104177779, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104178143, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104178375, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104178723, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104179038, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104179397, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104179772, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104180098, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104180524, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104181280, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104182101, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104182632, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104182892, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104183637, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104184124, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104184880, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104185103, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104185165, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104185411, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104185552, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104186680, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104186841, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104188252, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104188426, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104189003, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104189161, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104189526, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749034104189612, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104189877, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104190326, "dur": 58664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104248992, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104251332, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104251391, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104253841, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104253933, "dur": 2709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104256643, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104257184, "dur": 2610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749034104259795, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034104260099, "dur": 2969307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749034107229421, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749034107229407, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749034107229589, "dur": 1491, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749034104141564, "dur": 27166, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104168737, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104169889, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1A11F72A8792752A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170018, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104170106, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170194, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B1A10C5722B27484.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170469, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170555, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170682, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170761, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104170904, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104171174, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104171282, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749034104171570, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749034104171830, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104171997, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104173770, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104173924, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104174124, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104174367, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104174597, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104175214, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104175866, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104176196, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104176601, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104176896, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104177259, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104177542, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104177785, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104178165, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104178564, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104178928, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104179366, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104179872, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104180203, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104180606, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104181586, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104182081, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104182622, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104182825, "dur": 1052, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104183882, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749034104184656, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104184781, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104184967, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104185157, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104185328, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749034104185959, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104186423, "dur": 3883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104190308, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749034104190509, "dur": 58491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104249003, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749034104251807, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749034104254469, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749034104257202, "dur": 2647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749034104259850, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104260091, "dur": 60057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749034104320149, "dur": 2911010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104141599, "dur": 27159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104168771, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749034104169887, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_42CC435405CEA14C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749034104169954, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104170027, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749034104170794, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749034104170993, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749034104171275, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749034104171601, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16372163617230684214.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749034104171730, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749034104171827, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104171990, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104173050, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104174222, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104174465, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104174726, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104174970, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104175193, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104175530, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104176213, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104176618, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104176852, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104177132, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104177381, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104177698, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104177987, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104178294, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104178704, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104178952, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104179628, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104180035, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104180723, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104181368, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104182162, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104182610, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749034104182854, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104183535, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104184597, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104184946, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104185161, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104185414, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749034104185594, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104186107, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104186411, "dur": 3900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104190311, "dur": 58686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104248999, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104251278, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104253663, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104253977, "dur": 2834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104256812, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104257137, "dur": 2788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749034104260073, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749034104260792, "dur": 2970383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104141626, "dur": 27151, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104168789, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104169654, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_6E9570E2A3C5474A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104169764, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4D5EF4ECE256D09B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104170018, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_6241537ACFF4D8B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104170455, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749034104170826, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749034104170924, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749034104171096, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749034104171285, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749034104171607, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749034104171754, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749034104171843, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104172184, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104173222, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-heap-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749034104173222, "dur": 1846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104175068, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104175953, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104176390, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104176681, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104176985, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104177263, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104177627, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104177932, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104178177, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104178594, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104178948, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104179248, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104179733, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104180101, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104180495, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104181441, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104182145, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104182633, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104182899, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104183719, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104184049, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104184141, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104184948, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104185159, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104185416, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749034104185598, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104186033, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104186100, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104186477, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104186611, "dur": 3706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104190317, "dur": 58705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104249034, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104251628, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104254668, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104254870, "dur": 2554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104257425, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749034104257489, "dur": 2538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749034104260116, "dur": 2971016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104141661, "dur": 27133, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104168805, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104170012, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104170139, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104170492, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749034104170564, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749034104170704, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749034104170894, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749034104171281, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749034104171641, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749034104171708, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749034104171858, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104173328, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749034104172249, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104174050, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104174253, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104174457, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104174736, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104174969, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104175468, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104176142, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104176497, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104176761, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104177105, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104177330, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104177599, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104178190, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104178487, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104178883, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104179396, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104179945, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104180215, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104180585, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104181250, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104182061, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104182605, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104182817, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104183282, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104184098, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104184718, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104184965, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104185159, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104185424, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104185925, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104186400, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104186590, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104186909, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104187290, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104187389, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104187982, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749034104188129, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104188634, "dur": 1721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104190355, "dur": 59144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749034104249501, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104251889, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104254446, "dur": 2831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104257331, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749034104260120, "dur": 2971011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104141684, "dur": 27128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104168823, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104169921, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104170015, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104170253, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0F4B26DD7F6C67C4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104170639, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749034104170897, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749034104171134, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749034104171275, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749034104171397, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749034104171622, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749034104171730, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749034104171828, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104171993, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104173177, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104174738, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104175247, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104175501, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104176109, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104176440, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104176831, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104177120, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104177343, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104177626, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104178033, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104178781, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104179126, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104179638, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104180024, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104180460, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104180967, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104181526, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104182098, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104182612, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104182836, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749034104183346, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104183597, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104183989, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104184207, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749034104185045, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104185192, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104185437, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104185923, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104186402, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749034104186634, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749034104187188, "dur": 3194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104190382, "dur": 58648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104249031, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749034104251686, "dur": 1338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104253036, "dur": 3284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749034104256377, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749034104259441, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104259549, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104259644, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104259751, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749034104260110, "dur": 2971025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104141711, "dur": 27119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104168839, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749034104169764, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104169909, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749034104170029, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104170179, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749034104170386, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749034104170717, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749034104170860, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749034104171114, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749034104171257, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749034104171697, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749034104171814, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104171980, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104173016, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104174891, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104175416, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104175976, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104176253, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104176554, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104176866, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104177229, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104177554, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104177957, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104178347, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104178736, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104179019, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104179336, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104179776, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104180031, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104180569, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104181088, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104181308, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104182096, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104182614, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749034104182862, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104183269, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104183795, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104183917, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749034104184120, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104184986, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104185155, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749034104185320, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104185849, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104185953, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104186410, "dur": 3902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104190313, "dur": 58682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104249022, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104251951, "dur": 2678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104254664, "dur": 1214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749034104255885, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104258701, "dur": 2029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749034104260777, "dur": 2970379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104141738, "dur": 27109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104168862, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749034104170020, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749034104170407, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749034104170650, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749034104170786, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749034104171136, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749034104171283, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749034104171520, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749034104171731, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749034104171865, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104172429, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104174178, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104174444, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104174689, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104175033, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104175177, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104175478, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104176172, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104176552, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104176803, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104177151, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104177407, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104177717, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104178095, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104178257, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104178665, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104179036, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104179414, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104179880, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104180058, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104180343, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104180577, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104181510, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104182137, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104182624, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749034104182887, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749034104183072, "dur": 498, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749034104183571, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749034104184341, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104184771, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104184976, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104185158, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104185417, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749034104185610, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749034104186010, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104186143, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104186408, "dur": 2602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104189010, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104190337, "dur": 58678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104249016, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749034104251566, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104251677, "dur": 2713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749034104254391, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104254558, "dur": 3036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749034104257595, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034104257662, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749034104260101, "dur": 2969309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034107229427, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749034107229411, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749034107229502, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749034107229582, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104141765, "dur": 27098, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104168873, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104169767, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104169879, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68DB32F33070957F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104170027, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104170366, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749034104170513, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749034104170872, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749034104170978, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749034104171261, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749034104171344, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749034104171541, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749034104171630, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749034104171798, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104173542, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.43f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749034104172195, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104174080, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104174314, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104174849, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104175104, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104175259, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104175894, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104176141, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104176692, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104177003, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104177308, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104177585, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104177927, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104178203, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104178645, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104178892, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104179153, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104179506, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104179897, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104180383, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104180469, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104181250, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104182062, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104182603, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104182808, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104182870, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104183688, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104184031, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104184850, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104185081, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104185213, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104185609, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104185950, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104186405, "dur": 1900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034104188307, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104188430, "dur": 1102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104189572, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104189674, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104190310, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749034104190407, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104190698, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034104192734, "dur": 1562960, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034105760075, "dur": 4917, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749034105759706, "dur": 5380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749034105765158, "dur": 11773, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749034105765154, "dur": 13266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749034105779785, "dur": 176, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034105780749, "dur": 1425192, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749034107229408, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749034107229399, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749034107229516, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749034107229586, "dur": 1579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104141788, "dur": 27094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104168893, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104169660, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7AD711F5F88D031D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104169748, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104169843, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104169995, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_DA6971D49F67486A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104170124, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA1900490E2D89BF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104170514, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749034104170716, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749034104170894, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749034104171024, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749034104171225, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749034104171370, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749034104171590, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6572595573279557027.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749034104171710, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749034104171820, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104171984, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104173528, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104173754, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104173998, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104174190, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104174448, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104174671, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104175004, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104175272, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104175921, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104176190, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104176605, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104176945, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104177282, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104177593, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104177896, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104178273, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104178474, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104178954, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104179505, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104180047, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104180563, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104181425, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104182153, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104182852, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104183067, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104183377, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104184247, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104184306, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104184499, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104185208, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104185368, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104185924, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104186399, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749034104186657, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104187093, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104187237, "dur": 3127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104190364, "dur": 59130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104249495, "dur": 2619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104252115, "dur": 926, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104253053, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104255562, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104255824, "dur": 3027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749034104258852, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259018, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259247, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259303, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259486, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259560, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259832, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104259990, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749034104260126, "dur": 2971017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104141815, "dur": 27082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104168898, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104169663, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_774893DE4F1F8941.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104169743, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104169919, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104170002, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104170298, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4D1C445C9CC0E084.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104170422, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104170581, "dur": 9710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104180292, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104180431, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104180781, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104182095, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104182196, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104182621, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104182832, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104183410, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104183728, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104183997, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104184744, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104184849, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104185033, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104185204, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104185429, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104185922, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104186404, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749034104186672, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104187239, "dur": 3133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104190372, "dur": 58655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104249029, "dur": 2722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104251752, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104252010, "dur": 2967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104255014, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104257762, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749034104258331, "dur": 2178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749034104260563, "dur": 2970621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749034107236951, "dur": 1245, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 7120, "tid": 46, "ts": 1749034107256029, "dur": 3339, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 7120, "tid": 46, "ts": 1749034107259424, "dur": 1692, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 7120, "tid": 46, "ts": 1749034107251743, "dur": 10277, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}