{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5696, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5696, "tid": 44, "ts": 1749042930001574, "dur": 744, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749042930006424, "dur": 621, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5696, "tid": 1, "ts": 1749042929624350, "dur": 4698, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749042929629053, "dur": 61699, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749042929690764, "dur": 58699, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749042930007048, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 5696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929622326, "dur": 12708, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929635036, "dur": 356742, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929635913, "dur": 2965, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929638886, "dur": 1399, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640288, "dur": 236, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640527, "dur": 11, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640539, "dur": 68, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640611, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640614, "dur": 45, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640662, "dur": 33, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640698, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640700, "dur": 89, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640792, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640833, "dur": 32, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640868, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640870, "dur": 41, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640913, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640914, "dur": 38, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640954, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640956, "dur": 28, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929640986, "dur": 26, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641015, "dur": 29, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641047, "dur": 28, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641077, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641079, "dur": 30, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641112, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641140, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641167, "dur": 29, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641199, "dur": 26, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641227, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641254, "dur": 25, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641281, "dur": 24, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641308, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641338, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641366, "dur": 27, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641395, "dur": 25, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641424, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641451, "dur": 23, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641476, "dur": 23, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641501, "dur": 30, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641534, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641561, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641587, "dur": 30, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641619, "dur": 23, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641645, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641647, "dur": 27, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641676, "dur": 24, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641702, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641703, "dur": 28, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641733, "dur": 23, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641759, "dur": 27, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641789, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641816, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641842, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641873, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641896, "dur": 27, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641925, "dur": 29, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641956, "dur": 26, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929641985, "dur": 24, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642012, "dur": 23, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642037, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642061, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642086, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642115, "dur": 19, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642136, "dur": 24, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642162, "dur": 27, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642192, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642219, "dur": 29, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642250, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642273, "dur": 36, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642313, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642351, "dur": 24, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642377, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642406, "dur": 30, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642439, "dur": 24, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642465, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642490, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642516, "dur": 28, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642547, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642572, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642574, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642604, "dur": 11, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642617, "dur": 33, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642654, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642703, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642705, "dur": 23, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642730, "dur": 28, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642760, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642786, "dur": 32, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642820, "dur": 26, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642848, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642850, "dur": 31, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642884, "dur": 27, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642913, "dur": 25, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642940, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642962, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929642985, "dur": 29, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643018, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643043, "dur": 28, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643074, "dur": 28, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643104, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643130, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643152, "dur": 25, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643179, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643204, "dur": 30, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643236, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643262, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643283, "dur": 75, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643363, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643400, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643402, "dur": 22, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643426, "dur": 27, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643455, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643479, "dur": 26, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643508, "dur": 176, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643686, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643714, "dur": 34, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643751, "dur": 27, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643780, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643806, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643828, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643830, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643852, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643902, "dur": 35, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643939, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643940, "dur": 36, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929643978, "dur": 28, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644008, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644009, "dur": 28, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644040, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644054, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644082, "dur": 27, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644111, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644140, "dur": 24, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644167, "dur": 42, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644214, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644252, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644253, "dur": 25, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644281, "dur": 24, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644308, "dur": 25, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644335, "dur": 22, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644360, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644391, "dur": 25, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644419, "dur": 23, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644444, "dur": 25, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644471, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644497, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644523, "dur": 31, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644556, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644584, "dur": 28, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644615, "dur": 27, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644644, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644672, "dur": 27, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644702, "dur": 28, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644732, "dur": 30, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644765, "dur": 23, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644790, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644792, "dur": 26, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644820, "dur": 34, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644856, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644858, "dur": 42, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644903, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644905, "dur": 31, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644939, "dur": 24, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644966, "dur": 29, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929644998, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645029, "dur": 32, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645063, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645087, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645113, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645136, "dur": 42, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645180, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645207, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645208, "dur": 27, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645238, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645265, "dur": 23, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645291, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645312, "dur": 32, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645346, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645348, "dur": 23, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645373, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645401, "dur": 24, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645428, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645461, "dur": 22, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645485, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645512, "dur": 26, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645541, "dur": 28, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645572, "dur": 25, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645599, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645628, "dur": 26, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645656, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645657, "dur": 36, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645695, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645719, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645746, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645770, "dur": 29, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645801, "dur": 26, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645830, "dur": 25, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645857, "dur": 25, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645886, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645910, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645936, "dur": 21, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645959, "dur": 23, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929645984, "dur": 30, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646017, "dur": 30, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646049, "dur": 22, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646075, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646102, "dur": 28, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646131, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646133, "dur": 28, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646163, "dur": 24, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646189, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646191, "dur": 26, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646220, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646252, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646254, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646278, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646281, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646303, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646336, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646356, "dur": 30, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646390, "dur": 25, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646417, "dur": 22, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646441, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646469, "dur": 31, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646502, "dur": 24, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646528, "dur": 38, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646569, "dur": 22, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646593, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646619, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646644, "dur": 25, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646671, "dur": 29, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646703, "dur": 26, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646731, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646755, "dur": 24, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646782, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646811, "dur": 25, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646838, "dur": 24, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646864, "dur": 32, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646899, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646930, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646932, "dur": 24, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646958, "dur": 32, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929646993, "dur": 25, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647020, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647022, "dur": 27, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647052, "dur": 25, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647079, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647108, "dur": 28, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647139, "dur": 26, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647168, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647192, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647194, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647218, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647245, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647273, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647297, "dur": 25, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647324, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647348, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647369, "dur": 23, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647394, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647416, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647417, "dur": 33, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647454, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647456, "dur": 33, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647491, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647493, "dur": 27, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647522, "dur": 29, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647552, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647554, "dur": 29, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647586, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647587, "dur": 38, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647627, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647630, "dur": 39, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647671, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647672, "dur": 30, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647703, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647718, "dur": 28, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647748, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647750, "dur": 25, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647780, "dur": 33, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647817, "dur": 36, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647855, "dur": 1, "ph": "X", "name": "ProcessMessages 1046", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647857, "dur": 27, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647887, "dur": 26, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647916, "dur": 25, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647943, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647969, "dur": 26, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647997, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929647999, "dur": 32, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648034, "dur": 35, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648072, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648074, "dur": 32, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648108, "dur": 28, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648139, "dur": 33, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648174, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648176, "dur": 39, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648219, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648221, "dur": 31, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648258, "dur": 34, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648293, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648294, "dur": 23, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648320, "dur": 31, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648355, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648394, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648396, "dur": 30, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648429, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648454, "dur": 25, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648480, "dur": 31, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648513, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648515, "dur": 29, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648546, "dur": 28, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648576, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648577, "dur": 26, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648607, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648632, "dur": 24, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648658, "dur": 28, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648689, "dur": 33, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648724, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648752, "dur": 26, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648781, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648783, "dur": 33, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648819, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648850, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648851, "dur": 26, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648879, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648904, "dur": 23, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648930, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648953, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929648984, "dur": 25, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649012, "dur": 37, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649052, "dur": 25, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649079, "dur": 23, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649104, "dur": 26, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649132, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649134, "dur": 31, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649167, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649168, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649200, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649227, "dur": 26, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649255, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649286, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649287, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649318, "dur": 28, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649349, "dur": 47, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649399, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649401, "dur": 53, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649457, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649460, "dur": 54, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649516, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649519, "dur": 45, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649566, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649567, "dur": 34, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649603, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649605, "dur": 32, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649640, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649668, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649670, "dur": 27, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649699, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649724, "dur": 26, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649752, "dur": 25, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649779, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649780, "dur": 31, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649816, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649847, "dur": 47, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649900, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649941, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649943, "dur": 26, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929649971, "dur": 99, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650073, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650103, "dur": 27, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650134, "dur": 26, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650162, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650164, "dur": 27, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650193, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650221, "dur": 25, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650248, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650277, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650279, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650373, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650408, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650410, "dur": 30, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650442, "dur": 27, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650472, "dur": 75, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650550, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650580, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650611, "dur": 24, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650637, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650639, "dur": 78, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650719, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650750, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650752, "dur": 35, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650790, "dur": 73, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650866, "dur": 34, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650902, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929650930, "dur": 77, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651009, "dur": 2, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651012, "dur": 29, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651043, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651044, "dur": 85, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651134, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651168, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651169, "dur": 42, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651214, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651216, "dur": 41, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651260, "dur": 96, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651360, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651401, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651403, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651437, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651439, "dur": 70, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651512, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651546, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651586, "dur": 26, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651615, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651676, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651706, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651708, "dur": 30, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651740, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651742, "dur": 77, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651823, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651859, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651860, "dur": 28, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651892, "dur": 74, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929651970, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652003, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652004, "dur": 28, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652034, "dur": 26, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652062, "dur": 94, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652159, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652192, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652193, "dur": 30, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652227, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652260, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652261, "dur": 85, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652350, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652379, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652380, "dur": 30, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652413, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652414, "dur": 25, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652442, "dur": 72, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652517, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652562, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652565, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652594, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652596, "dur": 81, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652680, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652712, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652714, "dur": 29, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652744, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652746, "dur": 78, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652826, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652857, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652884, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652916, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929652917, "dur": 84, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653005, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653037, "dur": 28, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653067, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653069, "dur": 80, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653154, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653191, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653193, "dur": 24, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653219, "dur": 27, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653248, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653250, "dur": 31, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653283, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653284, "dur": 33, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653321, "dur": 23, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653346, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653372, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653462, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653494, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653496, "dur": 29, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653528, "dur": 27, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653558, "dur": 68, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653628, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653671, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653699, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653703, "dur": 33, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653740, "dur": 23, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653765, "dur": 71, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653839, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653871, "dur": 28, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653901, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653903, "dur": 27, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653931, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653933, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653961, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653963, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929653989, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654017, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654043, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654120, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654148, "dur": 28, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654178, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654179, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654206, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654275, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654305, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654331, "dur": 28, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654361, "dur": 24, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654388, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654415, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654443, "dur": 23, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654467, "dur": 1, "ph": "X", "name": "ProcessMessages 94", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654469, "dur": 24, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654495, "dur": 75, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654574, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654603, "dur": 38, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654645, "dur": 25, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654672, "dur": 69, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654744, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654773, "dur": 27, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654803, "dur": 29, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654833, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654835, "dur": 24, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654863, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654893, "dur": 24, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654921, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929654949, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655020, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655051, "dur": 25, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655079, "dur": 25, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655107, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655178, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655209, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655236, "dur": 24, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655262, "dur": 69, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655334, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655366, "dur": 27, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655397, "dur": 34, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655434, "dur": 27, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655464, "dur": 29, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655496, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655518, "dur": 23, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655544, "dur": 81, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655629, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655669, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655670, "dur": 27, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655700, "dur": 87, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655790, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655819, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655848, "dur": 80, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655929, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655956, "dur": 25, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929655984, "dur": 27, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656013, "dur": 34, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656050, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656084, "dur": 24, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656111, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656137, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656163, "dur": 73, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656238, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656266, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656293, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656320, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656321, "dur": 74, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656398, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656426, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656454, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656458, "dur": 23, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656483, "dur": 27, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656513, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656540, "dur": 89, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656632, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656666, "dur": 26, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656695, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656697, "dur": 31, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656730, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656732, "dur": 75, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656809, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656842, "dur": 29, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656873, "dur": 23, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656899, "dur": 79, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929656980, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657011, "dur": 23, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657037, "dur": 24, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657064, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657137, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657167, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657195, "dur": 77, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657274, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657305, "dur": 30, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657339, "dur": 22, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657364, "dur": 74, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657440, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657470, "dur": 23, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657496, "dur": 23, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657522, "dur": 76, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657600, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657634, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657636, "dur": 42, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657682, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657709, "dur": 27, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657739, "dur": 103, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657845, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657878, "dur": 28, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657910, "dur": 36, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657947, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929657949, "dur": 80, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658031, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658062, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658091, "dur": 83, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658177, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658205, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658231, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658233, "dur": 79, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658315, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658344, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658371, "dur": 28, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658401, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658402, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658471, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658500, "dur": 39, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658541, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658543, "dur": 86, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658634, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658667, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658694, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658721, "dur": 70, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658794, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658832, "dur": 26, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658862, "dur": 86, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658950, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929658977, "dur": 24, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659005, "dur": 23, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659032, "dur": 80, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659113, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659115, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659157, "dur": 103, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659264, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659266, "dur": 46, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659316, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659318, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659397, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659434, "dur": 32, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659467, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659469, "dur": 88, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659559, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659624, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659626, "dur": 36, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659665, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659667, "dur": 26, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659698, "dur": 74, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659777, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659816, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659818, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659847, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659874, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659955, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929659983, "dur": 44, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660030, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660033, "dur": 25, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660061, "dur": 64, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660128, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660158, "dur": 27, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660187, "dur": 24, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660215, "dur": 31, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660247, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660249, "dur": 30, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660281, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660284, "dur": 27, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660313, "dur": 29, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660347, "dur": 25, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660375, "dur": 81, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660459, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660486, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660513, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660540, "dur": 28, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660570, "dur": 26, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660598, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660599, "dur": 25, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660627, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660653, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660735, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660763, "dur": 23, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660789, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660816, "dur": 25, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660845, "dur": 27, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660873, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660875, "dur": 26, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660902, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660904, "dur": 24, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660932, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929660959, "dur": 63, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661024, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661053, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661054, "dur": 187, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661249, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661288, "dur": 328, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661619, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661682, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661685, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661724, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661726, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661773, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661776, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661824, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661827, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661879, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661884, "dur": 48, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661935, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661938, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661982, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929661986, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662035, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662037, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662085, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662089, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662137, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662140, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662185, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662187, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662225, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662227, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662269, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662271, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662311, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662314, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662353, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662357, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662402, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662405, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662446, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662448, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662492, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662495, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662539, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662541, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662588, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662591, "dur": 207, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662802, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662808, "dur": 39, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662850, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662854, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662892, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662895, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662938, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662941, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662978, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929662980, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663023, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663026, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663063, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663065, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663103, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663106, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663139, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663143, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663183, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663186, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663223, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663225, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663268, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663271, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663306, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663309, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663346, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663349, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663384, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663386, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663420, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663422, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663461, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663463, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663499, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663502, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663538, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663540, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663583, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663585, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663623, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663626, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663661, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663663, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663696, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663699, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663738, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663740, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663773, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663776, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663814, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663816, "dur": 39, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663859, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663862, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663904, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663907, "dur": 35, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663945, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663947, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663990, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929663994, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664031, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664034, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664060, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664064, "dur": 48, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664117, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664162, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664165, "dur": 48, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664217, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664220, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664262, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664265, "dur": 32, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664300, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664302, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664332, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664333, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664367, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664370, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664407, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664409, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664445, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664447, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664485, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664487, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664519, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664521, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664558, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664560, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664598, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664602, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664644, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664648, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664688, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664690, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664723, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664727, "dur": 42, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664772, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664774, "dur": 157, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664938, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664975, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929664977, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929665031, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929665065, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929665067, "dur": 6048, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671123, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671130, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671166, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671168, "dur": 493, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671667, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671714, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671717, "dur": 100, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671823, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671878, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929671880, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672003, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672047, "dur": 697, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672749, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672789, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672791, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672829, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672857, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672951, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929672981, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673275, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673305, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673336, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673540, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673574, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673576, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673620, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673622, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673648, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673675, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673706, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673709, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673740, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673742, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673773, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673839, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673867, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929673987, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674017, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674154, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674194, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674231, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674372, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674400, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674430, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674549, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674582, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674584, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674607, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674634, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674636, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674665, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674667, "dur": 112, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674786, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674817, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674848, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674849, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674879, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674881, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674900, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674942, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674975, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929674977, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675040, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675072, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675074, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675113, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675153, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675184, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675221, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675256, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675258, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675288, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675290, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675311, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675502, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675533, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675562, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675563, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675595, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675597, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675623, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675651, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675680, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675682, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675725, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675758, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675760, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675864, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675897, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675921, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675947, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929675949, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676127, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676161, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676227, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676256, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676290, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676325, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676356, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676358, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676460, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676462, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676497, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676499, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676532, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676534, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676564, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676566, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676655, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676695, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676736, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676739, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676804, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676840, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676878, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676879, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676910, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676914, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929676993, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677019, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677050, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677197, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677199, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677246, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677248, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677286, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677304, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677333, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677362, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677404, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677406, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677445, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677447, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677485, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677487, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677525, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677635, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677671, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677673, "dur": 132, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677808, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677843, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677845, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677881, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677882, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677966, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929677999, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678032, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678058, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678208, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678245, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678247, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678286, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678288, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678466, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678498, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678501, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678539, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678540, "dur": 278, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678824, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929678850, "dur": 642, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679496, "dur": 57, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679555, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679558, "dur": 123, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679684, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679716, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679747, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679779, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679780, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679883, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929679925, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680011, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680045, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680073, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680143, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680169, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680203, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680324, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680369, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680371, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680408, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680410, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680460, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680501, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680533, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680535, "dur": 240, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680780, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680811, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680813, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929680849, "dur": 473, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929681325, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929681355, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929681459, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929681488, "dur": 554, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682044, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682080, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682083, "dur": 163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682249, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682278, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682347, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682377, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682379, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682537, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929682563, "dur": 669, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929683235, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929683261, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929683265, "dur": 53688, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929736962, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929736967, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929736996, "dur": 1862, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929738862, "dur": 7048, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929745921, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929745925, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929745970, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746112, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746162, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746212, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746261, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746306, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746342, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746559, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746593, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929746596, "dur": 550, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747151, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747187, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747189, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747221, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747223, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747285, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929747317, "dur": 905, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748225, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748244, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748640, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748667, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748709, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748729, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748818, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748848, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748888, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748926, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748928, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929748962, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749023, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749049, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749157, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749184, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749186, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749343, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749365, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749563, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929749592, "dur": 736, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750333, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750369, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750371, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750402, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750451, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750478, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750759, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929750789, "dur": 646, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751438, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751478, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751546, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751582, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751615, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751617, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751758, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751790, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929751793, "dur": 434, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752231, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752265, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752358, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752394, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752396, "dur": 290, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752690, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752722, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752724, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752914, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929752943, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929753012, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929753043, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929753265, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929753267, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929753290, "dur": 960, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754254, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754282, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754283, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754318, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754320, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754351, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754353, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754385, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754419, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754421, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754452, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754490, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754525, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754530, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754562, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754589, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754612, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754647, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754650, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754679, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754681, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754707, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754742, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754745, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754776, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754778, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754811, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754813, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754854, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754856, "dur": 25, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754885, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754912, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754940, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754964, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929754999, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755020, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755065, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755101, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755103, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755136, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755138, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755172, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755174, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755207, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755209, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755237, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755238, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755267, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755269, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755300, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755301, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755342, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755344, "dur": 32, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755378, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755380, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755411, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755413, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755443, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755445, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755480, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755483, "dur": 27, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755511, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755513, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755554, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755556, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755588, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755654, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755683, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755768, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755808, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929755810, "dur": 388, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756203, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756245, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756246, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756276, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756278, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756312, "dur": 680, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929756999, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929757042, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929757043, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929757082, "dur": 70751, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929827843, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929827847, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929827876, "dur": 24, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929827901, "dur": 15504, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929843414, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929843418, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929843463, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929843465, "dur": 20565, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864041, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864046, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864098, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864102, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864167, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864218, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929864221, "dur": 73330, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929937560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929937563, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929937611, "dur": 20, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929937633, "dur": 16946, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929954592, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929954596, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929954622, "dur": 25, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929954647, "dur": 3922, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929958580, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929958585, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929958635, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929958638, "dur": 6704, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929965349, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929965353, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929965401, "dur": 7974, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929973385, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929973389, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929973446, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929973450, "dur": 1425, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929974879, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929974881, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929974911, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929974934, "dur": 5756, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929980695, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929980698, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929980736, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929980738, "dur": 532, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929981276, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929981302, "dur": 24, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929981327, "dur": 580, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929981912, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929981950, "dur": 278, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749042929982231, "dur": 9474, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749042930007060, "dur": 2345, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5696, "tid": 8589934592, "ts": 1749042929619690, "dur": 129800, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749042929749493, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749042929749499, "dur": 1261, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749042930009410, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5696, "tid": 4294967296, "ts": 1749042929602662, "dur": 390180, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749042929606268, "dur": 7999, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749042929992897, "dur": 5258, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749042929995864, "dur": 90, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749042929998258, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749042930009420, "dur": 20, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749042929634217, "dur": 1771, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929636000, "dur": 947, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929637081, "dur": 79, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749042929637160, "dur": 547, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929639346, "dur": 374, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749042929640858, "dur": 1267, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749042929652546, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749042929652788, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749042929660765, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749042929661167, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1749042929637743, "dur": 24897, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929662654, "dur": 320295, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929982951, "dur": 102, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929983055, "dur": 115, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929983397, "dur": 78, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929983504, "dur": 1042, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749042929637880, "dur": 24847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929662735, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929663228, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929663290, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_774893DE4F1F8941.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929663421, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_326BFA302771D2EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929663576, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_12B7B81D94FFC9B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929663670, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B0964A0CA2EEEE2D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929663730, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929663867, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929664033, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929664212, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929664404, "dur": 8802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929673269, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929673410, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929673592, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929674441, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929674553, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929674949, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929675081, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929675309, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929676081, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929676557, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929676640, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929676845, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929677744, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929677879, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929678877, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929679463, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929679900, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929681361, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749042929681511, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929681951, "dur": 1786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929683737, "dur": 63515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929747254, "dur": 2464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929749719, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929749827, "dur": 2461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929752289, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929752352, "dur": 3323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749042929755676, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929755930, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929756025, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929756113, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929756473, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929757083, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749042929757161, "dur": 1436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749042929758629, "dur": 224349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929637813, "dur": 24889, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929662710, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929663242, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_3359014152B0440F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929663386, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_30E5849A6622DFB0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929663578, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929663719, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929663858, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929663946, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8E726FC19BF52AA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929664283, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929664934, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749042929665158, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929665565, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929665739, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929665847, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929665958, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749042929666028, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929666181, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749042929666331, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929666749, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929667365, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929667536, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929667738, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929667931, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929668204, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929668441, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929668667, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929668891, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929669537, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929669789, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929670045, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929670522, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929670826, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929671033, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929671366, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929671733, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929672051, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929672303, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929672652, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929672973, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929673193, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929673833, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929674409, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929674972, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929675156, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929675729, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929676264, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929676687, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929676873, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749042929677228, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929677999, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929678300, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929678925, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929678983, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929679448, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929679903, "dur": 3810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929683713, "dur": 61524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929745240, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929748060, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929748745, "dur": 3269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929752058, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929754469, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749042929754864, "dur": 2946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749042929757857, "dur": 225112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929637793, "dur": 24897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929662709, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929662847, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929663286, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929663582, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_F66768BDA37A5632.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929663770, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929664165, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749042929664230, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929664451, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749042929664862, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749042929664951, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749042929665205, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929665392, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749042929665466, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929665559, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929665684, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749042929665754, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929665819, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749042929666055, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749042929666328, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929666863, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929667695, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929667890, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929668126, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929668476, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929668732, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929669353, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929669610, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929669925, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929670188, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929670456, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929670749, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929670956, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929671189, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929671417, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929671773, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929671985, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929672486, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929672776, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929673207, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929673886, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929674386, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929674947, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929675088, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929675160, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929676775, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929676870, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929677084, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929677182, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929678830, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929679087, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929679265, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929680585, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749042929680755, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929681316, "dur": 2417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929683733, "dur": 61498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929745233, "dur": 2862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929748155, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929750730, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929751159, "dur": 2480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929753640, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929753835, "dur": 2879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749042929756795, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929756999, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749042929757127, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749042929757395, "dur": 225565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929637760, "dur": 24909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929662706, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929662844, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929663266, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929663330, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1FDEA3861C839C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929663432, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929663517, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929663762, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_6241537ACFF4D8B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929664082, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_ABDE5DF73D4F51D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929664276, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929664343, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929664458, "dur": 471, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929665055, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749042929665504, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749042929665636, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929665779, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929666214, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749042929666349, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929667081, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929667760, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929667965, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929668334, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929668592, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929668837, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929669526, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929669772, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929670039, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929670307, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929670514, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929670826, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929671033, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929671302, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929671577, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929671870, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929672112, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929672545, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929672743, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929673061, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929673266, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929673736, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929674400, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929675004, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929675195, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929675299, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749042929676134, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929676454, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929676663, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749042929678204, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929678308, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929678471, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929678872, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929679437, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929679903, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749042929680121, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749042929680712, "dur": 3018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929683730, "dur": 62197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929745930, "dur": 3623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749042929749554, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929750240, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749042929752679, "dur": 670, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929753359, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749042929755869, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929756115, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929756509, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929756750, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749042929756927, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929757004, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749042929757146, "dur": 1449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749042929758637, "dur": 224344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929637861, "dur": 24854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929662723, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929663158, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929663284, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929663365, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_06983A7F9FE1EEBF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929663532, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929663721, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929664419, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929664692, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749042929664952, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929665276, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929665616, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929665690, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749042929665776, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929665871, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929666149, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749042929666314, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929666752, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929667621, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929667821, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929667998, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929668447, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929668711, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929668917, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929669546, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929669817, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929670031, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929670301, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929670509, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929670750, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929670999, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929671254, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929671493, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929671785, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929671990, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929672357, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929672725, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929673055, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929673377, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929674379, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929674951, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929675099, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929675161, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929675225, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929676188, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929676383, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929677170, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929677463, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929678333, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929678402, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929678871, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929679442, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929679890, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929680088, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929680587, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929680728, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929681350, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929681478, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929681943, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749042929682059, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929682394, "dur": 1346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929683740, "dur": 64022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929747764, "dur": 2666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929750492, "dur": 2501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749042929755026, "dur": 366, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1749042929755392, "dur": 1729, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1749042929757121, "dur": 156, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1749042929753036, "dur": 4251, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749042929757287, "dur": 225656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929637924, "dur": 24814, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929662746, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929663267, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929663355, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_54FADE420F1D3CF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929663513, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7FFEF131F3AD3BA4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929663712, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929663858, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929663920, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A38368206D763C77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929664153, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749042929664439, "dur": 509, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749042929665249, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929665624, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929665681, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749042929665942, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749042929666001, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749042929666102, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929666176, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749042929666342, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929666772, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929667512, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929667935, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929668309, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929668535, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929668786, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929669404, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929669664, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929669982, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929670243, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929670543, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929670798, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929671002, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929671377, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929671709, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929671942, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929672316, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929672594, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929672970, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929673177, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929673665, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929674397, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929675167, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929675342, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929675445, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929676258, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929676683, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929676827, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929676906, "dur": 1067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929677974, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929678114, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749042929678319, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929678812, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929678935, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929679447, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929679905, "dur": 3806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929683712, "dur": 61508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929745222, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929747475, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929749986, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929750630, "dur": 2532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929753163, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929753369, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929755801, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749042929755876, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749042929758611, "dur": 224379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929637964, "dur": 24789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929662764, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929663270, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929663433, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929663519, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929663575, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_94383F50AE2F3B27.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929663741, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929664338, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749042929664937, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665010, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665077, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665149, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665324, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665567, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665750, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929665842, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749042929665896, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929666311, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929666875, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929667537, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929667768, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929667983, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929668314, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929668552, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929668806, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929669429, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929669697, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929670043, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929670364, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929670586, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929670878, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929671113, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929671428, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929671772, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929671978, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929672408, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929672713, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929673131, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929673400, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929674403, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929675027, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929675227, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929675315, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929675590, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929676290, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929676465, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929676540, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929676672, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929676750, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929676872, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929677202, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929677264, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929678124, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929678283, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929678861, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929679067, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929679634, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929679902, "dur": 1749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929681653, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929681791, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929682947, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929683058, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929683732, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749042929683851, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929684139, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929685340, "dur": 144091, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929834038, "dur": 10788, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749042929833665, "dur": 11240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929844968, "dur": 17143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749042929844965, "dur": 18683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929865216, "dur": 275, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749042929866149, "dur": 72989, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749042929959975, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749042929959966, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749042929960122, "dur": 22872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929638007, "dur": 24760, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929662777, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749042929663246, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_6E9570E2A3C5474A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749042929663335, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929663549, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_233FB08ADCB5306F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749042929663651, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_AFAA4BAD90F58029.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749042929663703, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929664339, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749042929664527, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929664713, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929664791, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929664896, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929665051, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749042929665334, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929665687, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749042929665890, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929665961, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749042929666073, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749042929666135, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929666228, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749042929666326, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929666857, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929667728, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929667934, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929668234, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929668534, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929668792, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929669441, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929669769, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929670247, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929670579, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929670847, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929671104, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929671397, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929671729, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929672032, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929672375, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929672698, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929673158, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929673478, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929674395, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929675038, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749042929675226, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929675761, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749042929676586, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929676695, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929676925, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749042929677102, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749042929677830, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929677910, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929678884, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929679446, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929679916, "dur": 3805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929683721, "dur": 61493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929745216, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749042929747707, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929747914, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749042929750158, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929750540, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749042929753112, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929753204, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749042929755813, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929756014, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929756433, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929757076, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749042929757179, "dur": 202793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749042929959996, "dur": 6876, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749042929959974, "dur": 6899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749042929966913, "dur": 16075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929638043, "dur": 24741, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929662797, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929663283, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7AD711F5F88D031D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929663544, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68DB32F33070957F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929663630, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68DB32F33070957F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929663781, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929664040, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929664176, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929664370, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929664571, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929665006, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929665192, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929665371, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749042929665485, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749042929665564, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929665679, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749042929665769, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929665956, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929666026, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6572595573279557027.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749042929666317, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929666716, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929667501, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929667725, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929667930, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929668272, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929668498, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929668766, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929669395, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929669661, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929669988, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929670279, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929670539, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929670800, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929670996, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929671219, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929671485, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929671821, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929672038, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929672340, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929672611, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929672996, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929673208, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929673862, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929674408, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929674982, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929675205, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929675320, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749042929676055, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929676177, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929676263, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929676447, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929676503, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749042929677288, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929677523, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929677775, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929678883, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929679473, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929679915, "dur": 3793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929683710, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749042929683953, "dur": 61270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929745237, "dur": 3103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749042929748341, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929748797, "dur": 3096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749042929751894, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749042929751973, "dur": 2607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749042929754621, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749042929757383, "dur": 225575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929638077, "dur": 24725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929662811, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929663229, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929663346, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929663476, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_204AB48AF9442CC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929663569, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_1A11F72A8792752A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929663704, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929663864, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA1900490E2D89BF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929664016, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929664105, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929664374, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929664438, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749042929664727, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929664982, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929665194, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749042929665274, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929665544, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749042929665639, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929665730, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929666114, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929666243, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929666309, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929666790, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929667704, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929667919, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929668212, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929668622, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929668874, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929669556, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929669898, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929670161, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929670460, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929670754, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929670958, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929671161, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929671524, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929671855, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929672221, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929672460, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929672715, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929673041, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929673301, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929673377, "dur": 1013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929674390, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929675160, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929675504, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929675791, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929676379, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929676488, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929676686, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929676924, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929677271, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929677327, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929678145, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929678867, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929679442, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929679893, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929680074, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929680130, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929680766, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929680938, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929681669, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929681782, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749042929681964, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929682414, "dur": 1302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929683717, "dur": 61511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929745230, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929748071, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929748169, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929750438, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929750757, "dur": 3130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929753889, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929753969, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749042929756677, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929757004, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749042929757135, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749042929757882, "dur": 225089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929638124, "dur": 24694, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929662827, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929663251, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929663327, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_DE30F0D9293C6825.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929663438, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929663550, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929663725, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929663854, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_36273396CDE3C103.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929663973, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0F4B26DD7F6C67C4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929664112, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929664310, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929664398, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929664605, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929664661, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929664950, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929665113, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929665200, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929665396, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929665459, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929665532, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929665665, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929665737, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929665839, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749042929665979, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929666289, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749042929666373, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929666628, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929667310, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929668013, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929668356, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929668576, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929668821, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929669441, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929669842, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929670068, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929670349, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929670709, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929670967, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929671205, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929671461, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929671748, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929672015, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929672384, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929672686, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929673035, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929673235, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929673779, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929674405, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929674992, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929675196, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929675272, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749042929676217, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929676417, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929676553, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929676685, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929676926, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929677744, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749042929678037, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929678131, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749042929678638, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929678866, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929679448, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929679897, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929680591, "dur": 3150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929683741, "dur": 62564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929746307, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749042929748790, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929748882, "dur": 2933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749042929751816, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929751927, "dur": 4828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749042929757117, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749042929757288, "dur": 225653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929638163, "dur": 24670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929662843, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929663725, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929664081, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749042929664203, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929664282, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749042929664544, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749042929664869, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929665028, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929665275, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749042929665563, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929665665, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749042929665750, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929665940, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749042929666119, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929666328, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929666526, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929667227, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929668118, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929668407, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929668839, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929669486, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929669803, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929670005, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929670306, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929670548, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929670851, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929671052, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929671385, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929671663, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929671895, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929672224, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929672524, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929672962, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929673413, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929674387, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929674945, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929675156, "dur": 1465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929676621, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929676812, "dur": 905, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929677730, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929678054, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929678857, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929679059, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929679901, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929680099, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929680706, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929680879, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929681644, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749042929681756, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929682120, "dur": 1611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929683732, "dur": 61642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929745385, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929747574, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929747721, "dur": 5168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929752890, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749042929753140, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929755858, "dur": 2673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749042929758600, "dur": 224374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929638255, "dur": 24621, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929662886, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749042929663709, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929663844, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749042929664165, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929664284, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929664416, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929664654, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929664872, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929665028, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929665153, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929665283, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929665507, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929665690, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929665805, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929665976, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929666102, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749042929666322, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929666540, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929667243, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929667810, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929668083, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929668377, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929668595, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929668841, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929669457, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929669835, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929670052, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929670446, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929670755, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929670965, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929671197, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929671493, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929671785, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929672009, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929672280, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929672531, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929672807, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929673378, "dur": 999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929674404, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929675014, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749042929675251, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929677090, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929677205, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929677319, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749042929677532, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929678291, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929678859, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749042929679034, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929679442, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1749042929679912, "dur": 141, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929680436, "dur": 58114, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1749042929745233, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929747503, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929747704, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929750208, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929750544, "dur": 2586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929753187, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749042929755545, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929755960, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929756822, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929756906, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749042929756972, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929757256, "dur": 217500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749042929974783, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749042929974758, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749042929974948, "dur": 1527, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749042929976479, "dur": 6473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929638205, "dur": 24644, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929662860, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929663414, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929663467, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929663550, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_42CC435405CEA14C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929663642, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929663733, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929663914, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929664257, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929664451, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929664552, "dur": 8074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929672690, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929672772, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929673394, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929674413, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929674960, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929675105, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929675168, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929676004, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929676644, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929676855, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929677543, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929677783, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929677923, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929678865, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929679097, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749042929679230, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929680453, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929680567, "dur": 3157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929683724, "dur": 61487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929745213, "dur": 2368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929747582, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929747810, "dur": 2902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929750713, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929750952, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929753682, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929753953, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929756626, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929757083, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749042929757171, "dur": 76501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929833700, "dur": 28406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749042929833675, "dur": 29966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929865561, "dur": 186, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749042929866274, "dur": 89890, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749042929974759, "dur": 7489, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749042929974749, "dur": 7501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749042929982272, "dur": 603, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749042929638295, "dur": 24602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929662908, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929663407, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929663488, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929663577, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929663701, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6675A272C2C435FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929664306, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929664510, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929664629, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749042929664754, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749042929664926, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749042929664986, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749042929665074, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929665570, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929665668, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749042929665908, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749042929665963, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929666177, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749042929666355, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929667177, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929667951, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929668371, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929668573, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929668836, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929669452, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929669770, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929669990, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929670257, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929670470, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929670734, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929670946, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929671210, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929671421, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929671722, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929671936, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929672203, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929672428, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929672737, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929673026, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929673373, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929674380, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929674949, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929675139, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929675237, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929676009, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929676609, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929677181, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929677247, "dur": 1615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929678863, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929679028, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929679593, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929679911, "dur": 3795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929683708, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749042929683864, "dur": 61521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929745388, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929747712, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929750239, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929750311, "dur": 3906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929754218, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929754294, "dur": 2680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749042929756975, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929757060, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929757129, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749042929757826, "dur": 225141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929638334, "dur": 24578, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929662913, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929663463, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929663545, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_2972E28E15539B90.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929663709, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_DA6971D49F67486A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929663968, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F4B95EF39F1A8010.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929664405, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749042929664693, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749042929664958, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929665145, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749042929665557, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749042929665609, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929665786, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929665961, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929666111, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929666228, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749042929666326, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929666547, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929667373, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929667598, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929667801, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929668021, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929668285, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929668496, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929668749, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929669389, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929669630, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929669931, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929670200, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929670499, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929670999, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929671315, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929671567, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929671842, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929672075, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929672348, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929672629, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929672869, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929672998, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929673216, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929673743, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929674402, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929675050, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929675364, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929676179, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929676440, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929677147, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929677864, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929678878, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929679438, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929679896, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749042929680058, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929680650, "dur": 3075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929683726, "dur": 61491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929745232, "dur": 2599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929747833, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929747888, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929750422, "dur": 2940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929753362, "dur": 1145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749042929754518, "dur": 3230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749042929757809, "dur": 225153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749042929991365, "dur": 1500, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5696, "tid": 44, "ts": 1749042930010180, "dur": 3699, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5696, "tid": 44, "ts": 1749042930013920, "dur": 1323, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749042930004636, "dur": 11399, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}