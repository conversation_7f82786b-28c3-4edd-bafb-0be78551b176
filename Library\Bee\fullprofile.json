{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 7504, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 7504, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 7504, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 7504, "tid": 9, "ts": 1749665706132115, "dur": 937, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 7504, "tid": 9, "ts": 1749665706138854, "dur": 629, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 7504, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 7504, "tid": 1, "ts": 1749665705717158, "dur": 16470, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7504, "tid": 1, "ts": 1749665705733633, "dur": 65573, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7504, "tid": 1, "ts": 1749665705799217, "dur": 42060, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 7504, "tid": 9, "ts": 1749665706139487, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 7504, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705715112, "dur": 31072, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705746186, "dur": 377865, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705747067, "dur": 2205, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705749281, "dur": 1570, "ph": "X", "name": "ProcessMessages 11005", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705750854, "dur": 187, "ph": "X", "name": "ReadAsync 11005", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751043, "dur": 10, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751054, "dur": 40, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751097, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751099, "dur": 54, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751163, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751166, "dur": 45, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751213, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751215, "dur": 42, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751259, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751260, "dur": 39, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751303, "dur": 38, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751343, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751345, "dur": 36, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751384, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751385, "dur": 36, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751426, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751427, "dur": 39, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751470, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751472, "dur": 39, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751514, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751517, "dur": 34, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751554, "dur": 37, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751593, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751595, "dur": 36, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751634, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751638, "dur": 33, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751675, "dur": 36, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751713, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751715, "dur": 29, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751747, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751748, "dur": 28, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751779, "dur": 28, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751809, "dur": 2, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751813, "dur": 39, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751853, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751855, "dur": 31, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751888, "dur": 31, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751921, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751922, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705751955, "dur": 83, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752040, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752075, "dur": 29, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752106, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752108, "dur": 32, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752141, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752143, "dur": 31, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752176, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752178, "dur": 25, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752207, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752236, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752264, "dur": 29, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752295, "dur": 28, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752326, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752328, "dur": 28, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752357, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752359, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752384, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752412, "dur": 3, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752417, "dur": 56, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752475, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752477, "dur": 24, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752505, "dur": 33, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752540, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752542, "dur": 27, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752571, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752574, "dur": 43, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752620, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752651, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752656, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752683, "dur": 24, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752709, "dur": 24, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752736, "dur": 28, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752767, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752789, "dur": 25, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752817, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752843, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752870, "dur": 24, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752896, "dur": 27, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752925, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752952, "dur": 29, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705752984, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753018, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753020, "dur": 58, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753081, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753083, "dur": 42, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753127, "dur": 2, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753130, "dur": 43, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753176, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753179, "dur": 38, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753219, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753220, "dur": 24, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753246, "dur": 43, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753292, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753294, "dur": 43, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753340, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753343, "dur": 38, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753383, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753385, "dur": 28, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753416, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753417, "dur": 29, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753449, "dur": 22, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753473, "dur": 33, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753509, "dur": 25, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753537, "dur": 50, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753589, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753590, "dur": 34, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753627, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753630, "dur": 25, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753657, "dur": 32, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753694, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753726, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753728, "dur": 27, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753759, "dur": 27, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753789, "dur": 42, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753834, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753837, "dur": 48, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753888, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753890, "dur": 33, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753925, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753926, "dur": 32, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753960, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753962, "dur": 26, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705753991, "dur": 34, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754028, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754060, "dur": 30, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754092, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754093, "dur": 31, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754127, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754129, "dur": 30, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754161, "dur": 25, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754189, "dur": 25, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754217, "dur": 24, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754243, "dur": 29, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754275, "dur": 38, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754317, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754319, "dur": 29, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754351, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754352, "dur": 32, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754388, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754390, "dur": 29, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754422, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754423, "dur": 22, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754447, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754475, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754500, "dur": 30, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754533, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754535, "dur": 31, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754567, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754568, "dur": 27, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754596, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754599, "dur": 23, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754624, "dur": 117, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754746, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754791, "dur": 2, "ph": "X", "name": "ProcessMessages 1871", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754795, "dur": 28, "ph": "X", "name": "ReadAsync 1871", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754826, "dur": 29, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754858, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705754860, "dur": 140, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755003, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755040, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755042, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755076, "dur": 30, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755109, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755110, "dur": 174, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755287, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755289, "dur": 45, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755336, "dur": 2, "ph": "X", "name": "ProcessMessages 2233", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755340, "dur": 34, "ph": "X", "name": "ReadAsync 2233", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755377, "dur": 31, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755410, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755412, "dur": 29, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755445, "dur": 32, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755481, "dur": 27, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755510, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755511, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755537, "dur": 40, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755580, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755618, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755620, "dur": 35, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755656, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755658, "dur": 29, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755689, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755691, "dur": 28, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755720, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755722, "dur": 26, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755753, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755786, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755788, "dur": 32, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755824, "dur": 29, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755855, "dur": 48, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755906, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755907, "dur": 25, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755935, "dur": 26, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755963, "dur": 23, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705755990, "dur": 28, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756019, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756021, "dur": 23, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756046, "dur": 28, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756076, "dur": 12, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756090, "dur": 28, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756121, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756150, "dur": 26, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756178, "dur": 26, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756208, "dur": 30, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756240, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756242, "dur": 25, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756270, "dur": 25, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756297, "dur": 24, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756324, "dur": 22, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756348, "dur": 24, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756374, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756375, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756403, "dur": 22, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756428, "dur": 29, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756460, "dur": 31, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756494, "dur": 27, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756522, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756524, "dur": 28, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756555, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756577, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756598, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756629, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756631, "dur": 26, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756659, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756661, "dur": 32, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756695, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756697, "dur": 25, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756725, "dur": 29, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756757, "dur": 31, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756790, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756792, "dur": 31, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756824, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756826, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756858, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756859, "dur": 30, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756891, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756893, "dur": 30, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756926, "dur": 34, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756962, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756964, "dur": 26, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705756993, "dur": 29, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757023, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757027, "dur": 25, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757055, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757082, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757110, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757112, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757139, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757142, "dur": 24, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757167, "dur": 21, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757190, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757215, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757244, "dur": 24, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757270, "dur": 23, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757295, "dur": 23, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757320, "dur": 25, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757348, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757373, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757397, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757420, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757448, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757475, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757477, "dur": 24, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757503, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757528, "dur": 29, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757560, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757561, "dur": 30, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757593, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757595, "dur": 29, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757627, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757650, "dur": 23, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757675, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757700, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757732, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757760, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757762, "dur": 27, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757792, "dur": 73, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757867, "dur": 23, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757893, "dur": 56, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757952, "dur": 27, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757981, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705757982, "dur": 29, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758014, "dur": 28, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758044, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758046, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758076, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758104, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758131, "dur": 27, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758160, "dur": 25, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758188, "dur": 1, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758189, "dur": 28, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758219, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758221, "dur": 26, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758249, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758280, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758304, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758306, "dur": 31, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758339, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758341, "dur": 30, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758374, "dur": 25, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758401, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758403, "dur": 55, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758461, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758489, "dur": 27, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758519, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758521, "dur": 33, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758556, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758557, "dur": 40, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758600, "dur": 21, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758623, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758653, "dur": 62, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758717, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758718, "dur": 27, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758748, "dur": 24, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758774, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758798, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758825, "dur": 28, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758854, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758856, "dur": 26, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758886, "dur": 28, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758916, "dur": 23, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758942, "dur": 26, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758971, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705758973, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759004, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759006, "dur": 25, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759033, "dur": 28, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759063, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759065, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759093, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759122, "dur": 25, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759149, "dur": 26, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759177, "dur": 22, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759201, "dur": 24, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759228, "dur": 25, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759256, "dur": 27, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759286, "dur": 23, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759326, "dur": 31, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759360, "dur": 26, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759388, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759413, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759436, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759466, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759468, "dur": 34, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759504, "dur": 23, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759531, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759558, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759581, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759601, "dur": 26, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759630, "dur": 32, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759665, "dur": 30, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759697, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759699, "dur": 28, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759729, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759730, "dur": 41, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759774, "dur": 28, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759803, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759805, "dur": 26, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759833, "dur": 27, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759863, "dur": 24, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759890, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759916, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759942, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759961, "dur": 21, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705759984, "dur": 40, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760027, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760064, "dur": 26, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760092, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760093, "dur": 25, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760121, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760122, "dur": 30, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760155, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760157, "dur": 37, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760196, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760198, "dur": 30, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760230, "dur": 26, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760259, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760285, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760313, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760348, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760374, "dur": 25, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760401, "dur": 26, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760430, "dur": 23, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760455, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760485, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760518, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760520, "dur": 28, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760549, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760553, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760581, "dur": 32, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760616, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760638, "dur": 24, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760665, "dur": 23, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760690, "dur": 24, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760716, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760718, "dur": 30, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760749, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760751, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760778, "dur": 23, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760804, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760832, "dur": 27, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760861, "dur": 25, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760890, "dur": 28, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760919, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760920, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760949, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705760974, "dur": 25, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761001, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761002, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761028, "dur": 26, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761057, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761086, "dur": 29, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761118, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761148, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761173, "dur": 26, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761201, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761203, "dur": 24, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761229, "dur": 22, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761254, "dur": 22, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761279, "dur": 24, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761307, "dur": 30, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761338, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761340, "dur": 23, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761366, "dur": 53, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761422, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761446, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761472, "dur": 23, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761497, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761498, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761523, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761548, "dur": 43, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761594, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761622, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761624, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761651, "dur": 94, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761748, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761781, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761784, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761813, "dur": 29, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761844, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761846, "dur": 25, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761873, "dur": 25, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761901, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761928, "dur": 26, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761957, "dur": 25, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705761984, "dur": 23, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762009, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762074, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762103, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762128, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762129, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762159, "dur": 81, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762243, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762274, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762275, "dur": 26, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762305, "dur": 78, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762385, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762418, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762443, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762474, "dur": 87, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762564, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762592, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762619, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762646, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762729, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762759, "dur": 29, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762791, "dur": 23, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762816, "dur": 71, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762890, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762927, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762929, "dur": 27, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705762959, "dur": 78, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763041, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763071, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763073, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763104, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763128, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763129, "dur": 81, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763213, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763242, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763266, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763290, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763368, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763398, "dur": 24, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763424, "dur": 37, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763463, "dur": 24, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763491, "dur": 74, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763568, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763601, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763603, "dur": 31, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763635, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763638, "dur": 76, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763716, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763718, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763743, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763745, "dur": 26, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763773, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763775, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763803, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763875, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763903, "dur": 29, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763935, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763937, "dur": 28, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705763969, "dur": 80, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764053, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764090, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764091, "dur": 27, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764120, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764122, "dur": 80, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764206, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764234, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764236, "dur": 31, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764269, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764270, "dur": 23, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764296, "dur": 74, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764372, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764406, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764431, "dur": 27, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764460, "dur": 81, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764543, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764571, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764573, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764601, "dur": 26, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764630, "dur": 70, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764702, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764730, "dur": 33, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764765, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764767, "dur": 28, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764798, "dur": 26, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764825, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764827, "dur": 25, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764854, "dur": 25, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764883, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764911, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764967, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705764970, "dur": 107, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765080, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765118, "dur": 4, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765123, "dur": 97, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765224, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765254, "dur": 76, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765332, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765360, "dur": 31, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765394, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765396, "dur": 26, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765424, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765426, "dur": 23, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765452, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765529, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765559, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765582, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765608, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765610, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765636, "dur": 29, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765666, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765667, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765694, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765721, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765722, "dur": 28, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765752, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765831, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765860, "dur": 28, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765890, "dur": 23, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765915, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765917, "dur": 65, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705765985, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766016, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766041, "dur": 25, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766069, "dur": 25, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766096, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766097, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766125, "dur": 26, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766153, "dur": 24, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766179, "dur": 23, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766205, "dur": 64, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766271, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766300, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766302, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766332, "dur": 44, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766380, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766382, "dur": 96, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766484, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766533, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766537, "dur": 40, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766581, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766583, "dur": 42, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766628, "dur": 4, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766633, "dur": 28, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766664, "dur": 32, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766698, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766700, "dur": 71, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766775, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766816, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766819, "dur": 41, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766863, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766866, "dur": 88, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766956, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766959, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766997, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705766999, "dur": 31, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767033, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767034, "dur": 28, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767065, "dur": 61, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767128, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767168, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767170, "dur": 29, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767201, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767202, "dur": 24, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767228, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767231, "dur": 31, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767265, "dur": 27, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767293, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767295, "dur": 27, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767325, "dur": 23, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767351, "dur": 76, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767431, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767466, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767468, "dur": 28, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767498, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767500, "dur": 78, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767580, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767612, "dur": 28, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767643, "dur": 22, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767668, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767670, "dur": 73, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767746, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767772, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767774, "dur": 27, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767803, "dur": 29, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767835, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767837, "dur": 31, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767871, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767872, "dur": 26, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767901, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767902, "dur": 28, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767933, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767935, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705767973, "dur": 24, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768000, "dur": 63, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768066, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768098, "dur": 29, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768131, "dur": 91, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768227, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768262, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768265, "dur": 32, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768300, "dur": 31, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768333, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768334, "dur": 28, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768364, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768366, "dur": 92, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768460, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768490, "dur": 33, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768527, "dur": 30, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768559, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768561, "dur": 77, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768642, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768676, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768679, "dur": 33, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768716, "dur": 23, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768742, "dur": 79, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768824, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768853, "dur": 28, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768885, "dur": 29, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768915, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705768918, "dur": 112, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769034, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769073, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769075, "dur": 35, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769114, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769116, "dur": 72, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769192, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769250, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769252, "dur": 36, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769292, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769294, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769366, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769368, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769399, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769401, "dur": 27, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769430, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769432, "dur": 31, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769465, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769466, "dur": 78, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769547, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769548, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769579, "dur": 27, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769609, "dur": 28, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769640, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769711, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769738, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769740, "dur": 24, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769766, "dur": 23, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769793, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769866, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769894, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769898, "dur": 32, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705769932, "dur": 72, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770006, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770037, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770039, "dur": 31, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770072, "dur": 4, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770077, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770104, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770106, "dur": 74, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770183, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770217, "dur": 27, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770248, "dur": 26, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770277, "dur": 86, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770366, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770395, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770397, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770423, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770425, "dur": 84, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770513, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770541, "dur": 23, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770568, "dur": 22, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770592, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770594, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770673, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770701, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770734, "dur": 20, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770756, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770758, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770825, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770856, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770882, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770884, "dur": 25, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770910, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770913, "dur": 76, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705770991, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771018, "dur": 26, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771045, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771047, "dur": 25, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771075, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771140, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771170, "dur": 90, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771262, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771266, "dur": 62, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771331, "dur": 24, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771357, "dur": 83, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771445, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771483, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771485, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771518, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771545, "dur": 85, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771633, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771660, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771689, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771719, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771744, "dur": 71, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771817, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771843, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771870, "dur": 23, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771897, "dur": 79, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705771979, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772011, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772038, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772040, "dur": 29, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772070, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772072, "dur": 33, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772108, "dur": 28, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772138, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772140, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772168, "dur": 28, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772198, "dur": 103, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772306, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772342, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772344, "dur": 32, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772379, "dur": 29, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772411, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772441, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772443, "dur": 28, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772473, "dur": 13, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772490, "dur": 24, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772517, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772542, "dur": 61, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772606, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772635, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772664, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772691, "dur": 29, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772722, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772725, "dur": 26, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772753, "dur": 25, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772782, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772810, "dur": 24, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772836, "dur": 61, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772902, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772935, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705772936, "dur": 142, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773084, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773142, "dur": 368, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773515, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773564, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773567, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773617, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773619, "dur": 44, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773667, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773670, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773716, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773720, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773765, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773768, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773807, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773811, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773852, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773855, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773902, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773909, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773955, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705773958, "dur": 45, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774006, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774009, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774048, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774049, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774112, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774157, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774161, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774203, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774205, "dur": 44, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774253, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774257, "dur": 40, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774301, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774304, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774347, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774349, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774391, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774394, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774441, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774444, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774468, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774471, "dur": 35, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774509, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774511, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774554, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774557, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774610, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774613, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774655, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774658, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774697, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774699, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774737, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774740, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774779, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774781, "dur": 32, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774817, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774820, "dur": 33, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774855, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774858, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774892, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774894, "dur": 34, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774932, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774935, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774973, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705774976, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775019, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775022, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775063, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775066, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775108, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775111, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775162, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775165, "dur": 44, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775212, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775215, "dur": 48, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775266, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775268, "dur": 41, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775313, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775316, "dur": 44, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775363, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775366, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775410, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775413, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775461, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775464, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775503, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775507, "dur": 45, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775555, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775558, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775605, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775609, "dur": 41, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775653, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775656, "dur": 39, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775699, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775702, "dur": 34, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775739, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775743, "dur": 44, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775790, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775794, "dur": 33, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775830, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775834, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775875, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775878, "dur": 33, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775915, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775917, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775963, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705775966, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776014, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776017, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776052, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776054, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776086, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776088, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776125, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776128, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776169, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776172, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776209, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776212, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776264, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776266, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776302, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776306, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776345, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776347, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705776382, "dur": 11727, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788116, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788119, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788182, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788185, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788231, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788263, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788265, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788370, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705788407, "dur": 639, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789051, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789090, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789123, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789210, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789324, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789353, "dur": 362, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789719, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789749, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789752, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705789790, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790018, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790020, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790055, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790058, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790096, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790098, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790140, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790142, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790186, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790190, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790223, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790226, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790374, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790400, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790403, "dur": 233, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790640, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790671, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790674, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790704, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790707, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790835, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790866, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790868, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790950, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790982, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705790985, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791022, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791024, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791060, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791062, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791091, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791093, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791122, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791160, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791193, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791328, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791357, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791359, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791388, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791390, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791456, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791481, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791524, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791552, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791575, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791577, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791605, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791606, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791642, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791644, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791671, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791673, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791710, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791712, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791742, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791744, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791781, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791783, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791815, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791856, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791887, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791889, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791922, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791965, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705791995, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792030, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792062, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792064, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792094, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792229, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792262, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792265, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792317, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792347, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792349, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792386, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792388, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792421, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792423, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792459, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792461, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792497, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792545, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792580, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792631, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792666, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792700, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792702, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792729, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792731, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792761, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792763, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705792787, "dur": 263, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793054, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793084, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793087, "dur": 135, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793227, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793260, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793263, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793293, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793297, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793331, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793361, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793363, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793444, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793481, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793622, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793661, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793664, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793696, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793699, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793734, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793736, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793763, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793797, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705793962, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794002, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794005, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794093, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794095, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794122, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794186, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794204, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794251, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794292, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794296, "dur": 161, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794462, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794492, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794638, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794686, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794724, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794754, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794757, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794796, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794798, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794844, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794883, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794885, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705794917, "dur": 753, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795676, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795742, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795745, "dur": 96, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795845, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795847, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795892, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795894, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705795920, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796026, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796069, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796103, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796141, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796184, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796186, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796222, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796224, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796270, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796299, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796340, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796513, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796563, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796565, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796609, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796611, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796692, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796728, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705796764, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797043, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797045, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797088, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797150, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797182, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797285, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797328, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797387, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797417, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797615, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797650, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797652, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797685, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797710, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797766, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797800, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797840, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797841, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705797998, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798038, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798040, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798089, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798117, "dur": 274, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798396, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798434, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798581, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798607, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798700, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798733, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798887, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798915, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705798916, "dur": 700, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705799619, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705799643, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705799646, "dur": 58571, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705858224, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705858228, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705858274, "dur": 1688, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705859968, "dur": 8403, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868380, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868385, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868436, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868438, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868496, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868538, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868541, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868606, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868649, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868651, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868688, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868690, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868787, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868805, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868838, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868872, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868874, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868903, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705868905, "dur": 590, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705869499, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705869531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705869533, "dur": 850, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870388, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870436, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870715, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870736, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870834, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870862, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870864, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870908, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870938, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705870941, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871136, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871163, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871165, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871198, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871224, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871226, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871346, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871375, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871377, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871447, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871450, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871481, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705871483, "dur": 576, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872062, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872064, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872101, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872103, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872144, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872171, "dur": 352, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872527, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705872553, "dur": 504, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873061, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873089, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873264, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873304, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873337, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873369, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873425, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873427, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873458, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873460, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873666, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873710, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705873989, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874022, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874165, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874193, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874255, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874283, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874285, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874451, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874493, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874497, "dur": 241, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874742, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874771, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705874773, "dur": 949, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875728, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875766, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875770, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875807, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875808, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875843, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875873, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875919, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875958, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875960, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705875998, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876044, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876046, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876087, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876126, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876128, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876190, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876223, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876270, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876272, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876304, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876334, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876336, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876366, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876395, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876397, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876428, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876429, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876469, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876471, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876510, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876512, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876541, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876584, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876586, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876622, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876624, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876659, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876661, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876702, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876705, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876742, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876744, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876774, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876776, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876804, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876806, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876838, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876840, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876873, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876875, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876912, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876914, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876952, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876955, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876989, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705876992, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877027, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877029, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877059, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877061, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877098, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877101, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877161, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877197, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877200, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877231, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877234, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877265, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877267, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877298, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877301, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877339, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877385, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877387, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877417, "dur": 230, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877652, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877687, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877689, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877719, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877746, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877772, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877799, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705877978, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878005, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878034, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878123, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878154, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878156, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878188, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878190, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878227, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878263, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878293, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878321, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878389, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878417, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878450, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705878453, "dur": 81382, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705959844, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705959849, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705959881, "dur": 21, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705959903, "dur": 14882, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705974795, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705974799, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705974853, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705974856, "dur": 20101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705994986, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705994994, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705995040, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705995045, "dur": 79, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705995128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705995131, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705995186, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665705995189, "dur": 80125, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706075333, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706075342, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706075379, "dur": 40, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706075420, "dur": 8327, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706083756, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706083760, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706083809, "dur": 27, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706083837, "dur": 10587, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706094429, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706094433, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706094477, "dur": 7425, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706101913, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706101920, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706101975, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706101978, "dur": 1450, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706103433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706103435, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706103479, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706103504, "dur": 2172, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706105680, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706105683, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706105723, "dur": 8526, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114258, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114263, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114292, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114295, "dur": 617, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114919, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114973, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706114999, "dur": 572, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706115575, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706115577, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706115620, "dur": 315, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 7504, "tid": 12884901888, "ts": 1749665706115937, "dur": 7793, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 7504, "tid": 9, "ts": 1749665706139501, "dur": 1986, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 7504, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 7504, "tid": 8589934592, "ts": 1749665705712403, "dur": 128944, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 7504, "tid": 8589934592, "ts": 1749665705841350, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 7504, "tid": 8589934592, "ts": 1749665705841354, "dur": 1138, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 7504, "tid": 9, "ts": 1749665706141489, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 7504, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 7504, "tid": 4294967296, "ts": 1749665705693429, "dur": 431498, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 7504, "tid": 4294967296, "ts": 1749665705696928, "dur": 9640, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 7504, "tid": 4294967296, "ts": 1749665706124983, "dur": 4500, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 7504, "tid": 4294967296, "ts": 1749665706127780, "dur": 85, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 7504, "tid": 4294967296, "ts": 1749665706129572, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 7504, "tid": 9, "ts": 1749665706141503, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749665705744371, "dur": 1863, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665705746245, "dur": 1325, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665705747690, "dur": 73, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749665705747763, "dur": 495, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665705748923, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665705750013, "dur": 1573, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665705752018, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665705754052, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749665705758303, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749665705759177, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1749665705761892, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665705765643, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749665705771704, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749665705771795, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749665705748281, "dur": 25146, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665705773443, "dur": 342056, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665706115501, "dur": 258, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665706115988, "dur": 63, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665706116085, "dur": 1046, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749665705748377, "dur": 25078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705773486, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705773606, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705774032, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705774117, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_54FADE420F1D3CF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705774480, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705774674, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705774790, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_0EF0635D76D99CD0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705774996, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749665705775099, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749665705775440, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749665705775505, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705775655, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749665705776238, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705776457, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749665705776739, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749665705776828, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705777628, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705778130, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705778341, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705779030, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705779344, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705779781, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705780351, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Runtime\\2D\\Light2DPoint.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749665705780224, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705781906, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705782134, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705782480, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705782709, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705782954, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705783289, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705783601, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705783821, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705784036, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705784344, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705784555, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705785292, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705785821, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705786322, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705786522, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705786751, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705787841, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705788729, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705789647, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705790335, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705790499, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705790693, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705791305, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705791679, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705791878, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705792165, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705792738, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705792861, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705793046, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705793647, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705793742, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705794227, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705794524, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665705794702, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705795138, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705795272, "dur": 3801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705799074, "dur": 67181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705866257, "dur": 3222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705869480, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705870011, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705872436, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705872575, "dur": 2811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705875388, "dur": 835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705876235, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665705878622, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665705878702, "dur": 236902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705748525, "dur": 25018, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705773556, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705773997, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705774094, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705774450, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705774626, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_36273396CDE3C103.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705774759, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705775243, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705775366, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749665705775536, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749665705775769, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1749665705776029, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1749665705776140, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749665705776530, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749665705776783, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705777216, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705777859, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705778301, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705778993, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705779567, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705780364, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\LightmappingShaderProperties.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665705780003, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705780947, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705781184, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705781798, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705782295, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705782621, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705782851, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705783095, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705783436, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705783944, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705784274, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705784881, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705785559, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705786171, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705786748, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705787195, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705788697, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705789596, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705790308, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705790476, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705790550, "dur": 2261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705792812, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705792978, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705793216, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705794653, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705794775, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705794908, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705795150, "dur": 1508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705796660, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705796830, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665705797023, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705797475, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705797585, "dur": 1409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705798994, "dur": 67237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705866233, "dur": 2540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705868773, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705868860, "dur": 2605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705871466, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705871866, "dur": 3010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705874878, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705874972, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665705877799, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705877907, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665705878709, "dur": 236885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705748456, "dur": 25045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705773509, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705773979, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705774054, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_DE30F0D9293C6825.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705774127, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705774195, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705774325, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705774484, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705774744, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_B5974478C0B40EC1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705774797, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705775252, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705775455, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749665705775548, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705775695, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749665705775819, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749665705776030, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749665705776441, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749665705776660, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749665705776771, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705777200, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705777976, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705778181, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705778428, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705779205, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705780194, "dur": 834, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Nodes\\Artistic\\Adjustment\\ReplaceColorNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749665705779744, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705781029, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705781641, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705782109, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705782443, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705782747, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705783016, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705783306, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705783870, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705784289, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705784790, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705785431, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705785994, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705786575, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705786849, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705787036, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705788361, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705788736, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705789605, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705790349, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705790740, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705790890, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705791879, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705792116, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705792406, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705793472, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705793862, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705794220, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705794530, "dur": 2052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705796584, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705796790, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705798064, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705798158, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705798280, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705798978, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665705799098, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705799409, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705800802, "dur": 159551, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705964732, "dur": 10333, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665705964325, "dur": 10819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705975146, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705975263, "dur": 16541, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665705975261, "dur": 18150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749665705994800, "dur": 242, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665705996081, "dur": 79680, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749665706094739, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749665706094725, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749665706094914, "dur": 20613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705748400, "dur": 25075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705773493, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705773592, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705774187, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705774472, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705774612, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4FC0F931AAC867AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705774696, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0B3C92E79B34D00C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705774757, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705774832, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_31DA0AEF6717D1CD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705774963, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705775078, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665705775768, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749665705775941, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749665705776503, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6572595573279557027.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665705776594, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6572595573279557027.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665705776734, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665705776791, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705777386, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705778143, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705778355, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705779053, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705779326, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705779743, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705780365, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\States\\NesterStateDescriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749665705780299, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705781104, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705781762, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705782760, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705783006, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705783423, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705784047, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705784316, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705784640, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705785119, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705785630, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705786163, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705786668, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705786890, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705787210, "dur": 1483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705788718, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705789610, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705790343, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705790649, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665705791749, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705791896, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705792094, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705792226, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705792909, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705792988, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705793287, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705794227, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705794534, "dur": 3170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705797706, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665705797906, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665705798359, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705798543, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705799004, "dur": 67224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705866231, "dur": 2666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749665705868898, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705869010, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749665705871660, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705871723, "dur": 3128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749665705874853, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705874995, "dur": 3115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749665705878111, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665705878237, "dur": 223805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665706102091, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749665706102046, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749665706102374, "dur": 1567, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749665706103946, "dur": 11568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705748439, "dur": 25051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705773500, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705773963, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705774042, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7AD711F5F88D031D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705774096, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705774249, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_30E5849A6622DFB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705774316, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B7E1353FAF528495.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705774430, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_ADF67561AEE86815.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705774486, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705774625, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_CB49B96AA053BB61.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705774737, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F4B95EF39F1A8010.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705774890, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705775103, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665705775343, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665705775424, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665705775615, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665705775758, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749665705775994, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705776454, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665705776751, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5334484173090267189.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665705776866, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705777685, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705778253, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705778484, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705779121, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705779442, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705780189, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\GraphSetup.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749665705780062, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705781348, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705781853, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705782088, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705782365, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705782617, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705783072, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705783342, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705784099, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705784553, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705785228, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705785741, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705786293, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705786741, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705786949, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705787644, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705788709, "dur": 893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705789602, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705790350, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705790591, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705790709, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705791369, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705791578, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705791799, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705791862, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705792644, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705792754, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705793004, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705793280, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705793547, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705794097, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705794233, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705794529, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665705794765, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705795465, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705795595, "dur": 3442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705799037, "dur": 67211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705866249, "dur": 2640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705868889, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705869025, "dur": 2541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705871567, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705871659, "dur": 2758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705874418, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705874521, "dur": 3879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665705878406, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665705878526, "dur": 236967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705748486, "dur": 25029, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705773524, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705774007, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_6E9570E2A3C5474A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705774095, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705774192, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B4A1BF472DE35F91.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705774370, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705774423, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B0964A0CA2EEEE2D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705774634, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705774778, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705775204, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1749665705775401, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749665705775543, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749665705775765, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749665705776381, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749665705776447, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749665705776805, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705777278, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705777974, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705778205, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705778492, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705779196, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705779661, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705780358, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Runtime\\2D\\Rendergraph\\DrawLight2DPass.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665705780167, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705781448, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705782096, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705782333, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705782622, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705782858, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705783083, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705783324, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705783987, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705784202, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705784497, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705785162, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705785710, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705786336, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705786572, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705786853, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705787165, "dur": 1532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705788698, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705789612, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705790306, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705790487, "dur": 660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705791157, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705791937, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705792185, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705792319, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705792517, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705792584, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705793151, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705793272, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705793538, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705794532, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705794760, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705795171, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705795295, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705795490, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705796402, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705796572, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665705796723, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705797080, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705797239, "dur": 1776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705799015, "dur": 67229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705866255, "dur": 2811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705869067, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705869156, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705871644, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705871732, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705874445, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705874701, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665705877233, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705877439, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705877547, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705877867, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665705878300, "dur": 237197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705748513, "dur": 25016, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705773541, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705774061, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1FDEA3861C839C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705774236, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DDBE5B8EC557ADE5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705774341, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_94383F50AE2F3B27.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705774488, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705774607, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_DA6971D49F67486A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705774726, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8E726FC19BF52AA2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705774779, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705775171, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665705775506, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705775819, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665705776002, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749665705776439, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665705776545, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665705776628, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705776702, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665705776769, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705777379, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705778178, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705778431, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705779075, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705779304, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705780112, "dur": 923, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Interfaces\\IMayRequirePosition.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749665705779822, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705781202, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705781959, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705782193, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705782772, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705783100, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705783352, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705784067, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705784287, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705784884, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705785625, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705786081, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705786504, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705786767, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705788482, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705788724, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705789635, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705790545, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705790805, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705791350, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705792256, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705792408, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705792982, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705793214, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705793864, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705794140, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705794226, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705794527, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705794764, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705795194, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705795355, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705795519, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705796227, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705796403, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665705796595, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705796987, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705797102, "dur": 1924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705799026, "dur": 68386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705867414, "dur": 2705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705870120, "dur": 774, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705870945, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705873473, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705873579, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705876204, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705876270, "dur": 2562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665705878834, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665705878932, "dur": 236617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705748552, "dur": 25019, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705773586, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665705774013, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705774397, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665705774496, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705774670, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705774780, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665705775211, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749665705775315, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705775651, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749665705775765, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749665705775916, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749665705775999, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705776058, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749665705776291, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749665705776421, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749665705776500, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749665705776614, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705776816, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705777622, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705778204, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705778426, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705779076, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705779301, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705780192, "dur": 810, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\ShaderInput.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749665705779882, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705781228, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705782022, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705782245, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705782455, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705782771, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705783026, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705783291, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705783711, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705783919, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705784190, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705784441, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705784672, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705785245, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705785696, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705786316, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705786690, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705786893, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705787232, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705788708, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705789617, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705790339, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665705790608, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705790669, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705791347, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705791509, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665705791861, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705792576, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705793025, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705793194, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705793274, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665705793530, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705794114, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705794221, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1749665705794919, "dur": 135, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705795449, "dur": 63311, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1749665705866257, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705868761, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705868858, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705871278, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705871358, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705873789, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705873859, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705876252, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705876520, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705876626, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705877565, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705877860, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705878198, "dur": 86134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705964361, "dur": 27456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665705964334, "dur": 29038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665705995314, "dur": 292, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665705996087, "dur": 88161, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665706102047, "dur": 12663, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665706102030, "dur": 12682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665706114736, "dur": 700, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665705748583, "dur": 25136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705773720, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705774202, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705774432, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9C249CA42F8C791A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705774656, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9C249CA42F8C791A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705774743, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0F4B26DD7F6C67C4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705774946, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705775107, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749665705775290, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749665705775402, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749665705775536, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749665705775764, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749665705776033, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749665705776506, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749665705776745, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749665705776859, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705777670, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705778294, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705778659, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705779148, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705779491, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705780360, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\Transitions\\NesterStateTransitionDescriptor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749665705780280, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705781177, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705781679, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705782488, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705782698, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705783001, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705783245, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705783458, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705784023, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705784241, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705784660, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705785295, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705785823, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705786495, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705786806, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705788184, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705788721, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705789631, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705790348, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705790631, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705790893, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749665705791774, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705792081, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705792222, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705792331, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705792564, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749665705793016, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705793187, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705793278, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705793527, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749665705794173, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705794294, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705794541, "dur": 4431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705798974, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665705799234, "dur": 67043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705866280, "dur": 2933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665705869214, "dur": 2004, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705871229, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665705873687, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705873944, "dur": 4165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665705878185, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665705878947, "dur": 236592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705748627, "dur": 24984, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705773622, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665705774543, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665705774671, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705774735, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_15E32D3DB1F12CD0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665705774892, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705775138, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749665705775247, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705775468, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749665705776407, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749665705776487, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749665705776743, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749665705776846, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705777705, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705778034, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705778227, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705778451, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705779114, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705779422, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705780348, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Interfaces\\Graph\\IEdge.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749665705779867, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705781096, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705781823, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705782460, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705782677, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705782891, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705783215, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705783424, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705783970, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705784221, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705784495, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705785196, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705785841, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705786351, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705786589, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705786825, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705787892, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705788723, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705789623, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705790340, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665705790574, "dur": 1664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705792239, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705792547, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665705792744, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705793227, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705793306, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705794247, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705794522, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665705794722, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705794778, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705795167, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705795291, "dur": 3764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705799056, "dur": 67210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705866268, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705868763, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705868912, "dur": 2931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705871845, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705871972, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705874770, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705875262, "dur": 2945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749665705878208, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665705878297, "dur": 237198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705748658, "dur": 24971, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705773645, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705774144, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705774198, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705774249, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705774385, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705774487, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705774940, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705775002, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705775198, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705775407, "dur": 13118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749665705788526, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705788651, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705788703, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705789593, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705790321, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705790636, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749665705791358, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705791537, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_0524057423981A9D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665705791589, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705791853, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705792306, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705792983, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705793286, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705794220, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705794536, "dur": 4444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705798980, "dur": 46937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705845918, "dur": 20346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705866265, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665705868916, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705868995, "dur": 2359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665705871355, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705871430, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665705874069, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705874187, "dur": 2451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665705876639, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705877010, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665705877112, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705877195, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705877322, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705877604, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705877752, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705877892, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665705878670, "dur": 236944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705748684, "dur": 24965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705773658, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705774092, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705774201, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705774501, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705774694, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705774997, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749665705775186, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705775266, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749665705775526, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705775667, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749665705776028, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749665705776109, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749665705776505, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16372163617230684214.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749665705776666, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705776738, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749665705776809, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705777474, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705778214, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705778459, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705779146, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705779384, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705780173, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\Vector3ShaderProperty.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749665705779880, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705781173, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705781988, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705782237, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705782776, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705783031, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705783290, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705783495, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705784096, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705784405, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705784608, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705785190, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705785682, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705786285, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705786783, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705788109, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705788712, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705789619, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705790353, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705790600, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705790853, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705791199, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705791992, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705792286, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705792542, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705792985, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705793184, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705793678, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705793951, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705794234, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705794531, "dur": 2305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705796838, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705797017, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705797579, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705797693, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705797806, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705798211, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665705798315, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705798623, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705798982, "dur": 67251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705866235, "dur": 2643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705868879, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705869116, "dur": 2689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705871806, "dur": 1960, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665705873779, "dur": 4807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665705878664, "dur": 236969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705748710, "dur": 24955, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705773678, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705774159, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705774266, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705774375, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705774450, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705774532, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705774712, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705774803, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_ABDE5DF73D4F51D2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705775060, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749665705775183, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705775375, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749665705775572, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749665705775664, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749665705776031, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749665705776253, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749665705776467, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749665705776786, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705777215, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705777908, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705778314, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705778935, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705779485, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705780186, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Runtime\\2D\\UTess2D\\Tessellator.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749665705780144, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705781539, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705782138, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705782707, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705783059, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705783298, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705783887, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705784111, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705784420, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705784679, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705785174, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705785713, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705786364, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705786622, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705786833, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705787044, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705787936, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705788715, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705789607, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705790311, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705790652, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705791763, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705791911, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705792072, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705792369, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705792588, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705792987, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705793259, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665705793467, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705793535, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705794111, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705794250, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705794539, "dur": 4437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705798977, "dur": 44028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705844457, "dur": 241, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 13, "ts": 1749665705844698, "dur": 1126, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 13, "ts": 1749665705845825, "dur": 78, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 13, "ts": 1749665705843007, "dur": 2904, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705845912, "dur": 20327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705866242, "dur": 2522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705868765, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705869001, "dur": 2546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705871548, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705871674, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705874579, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705874776, "dur": 2939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665705877716, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705877824, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705877909, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665705878810, "dur": 236755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705748737, "dur": 24945, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705773693, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665705774254, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_204AB48AF9442CC2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665705774509, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665705774736, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705774936, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705775003, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665705775153, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705775204, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749665705775357, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665705775915, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665705776140, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665705776354, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1749665705776470, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665705776546, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665705776767, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705777262, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705778003, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705778211, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705778522, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705779150, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705779694, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705780185, "dur": 803, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\ColorMaterialSlot.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749665705780133, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705781431, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705781957, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705782188, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705782704, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705782948, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705783158, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705783419, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705784084, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705784356, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705784561, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705785283, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705785781, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705786329, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705786578, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705786851, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705787173, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705788724, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705789628, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705790340, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665705790556, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705791385, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705791512, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705791573, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705792270, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705792395, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705792476, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705792977, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665705793154, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705793690, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705793822, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705794449, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705794602, "dur": 4462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705799064, "dur": 67198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705866273, "dur": 2726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705869000, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705869305, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705871938, "dur": 1099, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705873049, "dur": 3129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749665705876179, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705876360, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705876442, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705876840, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705877188, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705877602, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705877683, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705877887, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665705878528, "dur": 237117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705748763, "dur": 24937, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705773706, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705774166, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705774273, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705774393, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_50E1A15E13819776.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705774481, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705774622, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705774705, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4A1C632ABDE44C9A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705774995, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705775668, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749665705775992, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749665705776127, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749665705776802, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705777370, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705778094, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705778287, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705778547, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705779246, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705779713, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705780362, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@7a907cf5a459\\Editor\\BurstInspectorGUI.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749665705780240, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705781252, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705781754, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705782311, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705782568, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705782794, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705783138, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705783476, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705784225, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705784480, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705785175, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705785760, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705786367, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705786610, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705786815, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705788172, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705788719, "dur": 895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705789614, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705790324, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705790572, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705791383, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705791842, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705791977, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705792202, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705792865, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705793025, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705793285, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705794221, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705794529, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705794777, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665705794975, "dur": 1566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705796542, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705796674, "dur": 2372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705799047, "dur": 67203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705866251, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705868901, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705869404, "dur": 4207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705873612, "dur": 1061, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705874694, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749665705877480, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705877569, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705877857, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665705878205, "dur": 216525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665706094756, "dur": 11391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665706094733, "dur": 11416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665706106179, "dur": 9323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705748796, "dur": 24911, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705773716, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705774288, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705774465, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705774530, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_6241537ACFF4D8B0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705774766, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705774954, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_196389E2854E5BDF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705775140, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705775342, "dur": 13251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705788594, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705788760, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705788882, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705789662, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705789721, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705789839, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705790329, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705790525, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705791521, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705791904, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705792152, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705792681, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705792906, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705792975, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705793191, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705793813, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705794185, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705794248, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705794540, "dur": 4434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705798976, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665705799224, "dur": 67034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705866271, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705869090, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705869359, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705871850, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705872663, "dur": 3519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705876184, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665705876258, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749665705878797, "dur": 236784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665706122210, "dur": 1509, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 7504, "tid": 9, "ts": 1749665706142191, "dur": 3042, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 7504, "tid": 9, "ts": 1749665706145270, "dur": 1232, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 7504, "tid": 9, "ts": 1749665706137296, "dur": 10189, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}