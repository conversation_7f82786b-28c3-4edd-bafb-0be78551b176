{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5696, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5696, "tid": 44, "ts": 1749045165490769, "dur": 856, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749045165495183, "dur": 626, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5696, "tid": 1, "ts": 1749045165093920, "dur": 5451, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749045165099376, "dur": 67540, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749045165166931, "dur": 41396, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749045165495812, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 5696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165091852, "dur": 16184, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165108039, "dur": 373726, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165109056, "dur": 2068, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165111131, "dur": 2285, "ph": "X", "name": "ProcessMessages 11303", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113419, "dur": 283, "ph": "X", "name": "ReadAsync 11303", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113706, "dur": 11, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113718, "dur": 39, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113782, "dur": 44, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113829, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113831, "dur": 33, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113867, "dur": 29, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113899, "dur": 26, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113927, "dur": 40, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165113970, "dur": 36, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114009, "dur": 32, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114044, "dur": 26, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114073, "dur": 31, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114106, "dur": 25, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114135, "dur": 28, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114165, "dur": 29, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114197, "dur": 26, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114226, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114254, "dur": 27, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114283, "dur": 30, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114316, "dur": 24, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114342, "dur": 35, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114379, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114416, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114441, "dur": 47, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114514, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114551, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114553, "dur": 37, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114592, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114593, "dur": 38, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114635, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114637, "dur": 52, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114692, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114695, "dur": 69, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114768, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114801, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114803, "dur": 35, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114841, "dur": 30, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114875, "dur": 31, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114910, "dur": 30, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114942, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114943, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165114971, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115016, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115017, "dur": 31, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115050, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115052, "dur": 34, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115089, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115091, "dur": 63, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115158, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115161, "dur": 45, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115209, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115211, "dur": 40, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115253, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115255, "dur": 35, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115293, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115295, "dur": 34, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115332, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115334, "dur": 34, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115370, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115372, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115402, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115428, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115470, "dur": 26, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115498, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115499, "dur": 77, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115579, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115581, "dur": 31, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115614, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115616, "dur": 32, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115652, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115654, "dur": 32, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115688, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115690, "dur": 30, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115722, "dur": 30, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115756, "dur": 49, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115818, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115820, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115849, "dur": 41, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115894, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115896, "dur": 35, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115934, "dur": 25, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115961, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165115987, "dur": 25, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116014, "dur": 43, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116065, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116068, "dur": 28, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116098, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116099, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116122, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116125, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116159, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116161, "dur": 29, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116192, "dur": 29, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116224, "dur": 35, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116263, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116294, "dur": 29, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116326, "dur": 24, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116353, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116380, "dur": 30, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116413, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116439, "dur": 29, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116493, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116496, "dur": 33, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116530, "dur": 1, "ph": "X", "name": "ProcessMessages 1274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116532, "dur": 28, "ph": "X", "name": "ReadAsync 1274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116562, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116587, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116589, "dur": 26, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116618, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116620, "dur": 52, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116675, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116678, "dur": 34, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116713, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116714, "dur": 38, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116755, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116788, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116821, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116823, "dur": 30, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116855, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116857, "dur": 26, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116885, "dur": 23, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116912, "dur": 18, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116934, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116969, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116970, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116998, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165116999, "dur": 44, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117045, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117047, "dur": 26, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117076, "dur": 35, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117113, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117143, "dur": 31, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117177, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117179, "dur": 42, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117222, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117224, "dur": 26, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117253, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117277, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117299, "dur": 31, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117333, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117335, "dur": 32, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117370, "dur": 26, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117399, "dur": 54, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117455, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117481, "dur": 35, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117517, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117519, "dur": 29, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117564, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117566, "dur": 29, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117596, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117598, "dur": 141, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117741, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117777, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117778, "dur": 34, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117814, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117816, "dur": 36, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117854, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117856, "dur": 25, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117884, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117908, "dur": 28, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117938, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117974, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165117976, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118001, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118002, "dur": 26, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118032, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118061, "dur": 34, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118098, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118125, "dur": 28, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118156, "dur": 27, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118186, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118188, "dur": 60, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118250, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118251, "dur": 26, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118279, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118281, "dur": 25, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118307, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118309, "dur": 28, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118339, "dur": 21, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118363, "dur": 28, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118393, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118419, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118420, "dur": 23, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118447, "dur": 23, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118471, "dur": 25, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118500, "dur": 29, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118531, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118532, "dur": 36, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118571, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118573, "dur": 31, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118606, "dur": 23, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118632, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118673, "dur": 25, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118700, "dur": 26, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118729, "dur": 25, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118756, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118785, "dur": 22, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118809, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118833, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118857, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118886, "dur": 29, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118917, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118944, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118946, "dur": 23, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118971, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165118994, "dur": 23, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119020, "dur": 32, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119054, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119078, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119103, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119123, "dur": 29, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119155, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119188, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119190, "dur": 39, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119232, "dur": 23, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119258, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119281, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119310, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119311, "dur": 29, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119343, "dur": 27, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119373, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119396, "dur": 27, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119425, "dur": 17, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119444, "dur": 29, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119475, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119508, "dur": 25, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119535, "dur": 35, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119573, "dur": 25, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119601, "dur": 25, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119629, "dur": 24, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119656, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119680, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119708, "dur": 28, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119738, "dur": 32, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119773, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119797, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119829, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119830, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119862, "dur": 29, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119893, "dur": 26, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119921, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119923, "dur": 25, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119951, "dur": 38, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119991, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165119993, "dur": 25, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120020, "dur": 25, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120047, "dur": 23, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120073, "dur": 55, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120130, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120132, "dur": 36, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120171, "dur": 30, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120204, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120206, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120231, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120232, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120260, "dur": 26, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120288, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120289, "dur": 28, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120320, "dur": 21, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120343, "dur": 23, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120368, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120395, "dur": 27, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120424, "dur": 31, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120457, "dur": 24, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120484, "dur": 16, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120502, "dur": 21, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120526, "dur": 23, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120552, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120579, "dur": 26, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120607, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120632, "dur": 22, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120656, "dur": 29, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120687, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120690, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120724, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120726, "dur": 26, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120754, "dur": 25, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120781, "dur": 24, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120807, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120832, "dur": 29, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120863, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120891, "dur": 24, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120917, "dur": 24, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120944, "dur": 32, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165120979, "dur": 22, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121004, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121005, "dur": 26, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121033, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121059, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121060, "dur": 26, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121088, "dur": 25, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121116, "dur": 23, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121142, "dur": 21, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121165, "dur": 29, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121208, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121238, "dur": 26, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121267, "dur": 26, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121296, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121318, "dur": 10, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121330, "dur": 19, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121350, "dur": 23, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121375, "dur": 24, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121402, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121403, "dur": 36, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121441, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121466, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121486, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121515, "dur": 27, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121545, "dur": 21, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121568, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121591, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121611, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121636, "dur": 23, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121661, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121698, "dur": 23, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121723, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121724, "dur": 19, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121745, "dur": 27, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121775, "dur": 25, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121801, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121802, "dur": 23, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121827, "dur": 27, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121856, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121878, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121903, "dur": 33, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121939, "dur": 35, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121977, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165121979, "dur": 34, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122016, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122017, "dur": 32, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122051, "dur": 25, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122080, "dur": 29, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122113, "dur": 32, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122147, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122148, "dur": 23, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122173, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122198, "dur": 23, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122224, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122249, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122275, "dur": 22, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122299, "dur": 41, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122344, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122378, "dur": 25, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122406, "dur": 29, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122441, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122475, "dur": 23, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122502, "dur": 27, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122531, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122533, "dur": 30, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122565, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122567, "dur": 25, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122594, "dur": 30, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122626, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122628, "dur": 26, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122656, "dur": 25, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122683, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122685, "dur": 25, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122713, "dur": 31, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122746, "dur": 32, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122781, "dur": 1, "ph": "X", "name": "ProcessMessages 1035", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122783, "dur": 32, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122817, "dur": 30, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122849, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122851, "dur": 23, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122877, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122910, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122911, "dur": 25, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122938, "dur": 31, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122971, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165122973, "dur": 30, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123005, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123006, "dur": 26, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123035, "dur": 25, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123062, "dur": 24, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123089, "dur": 22, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123113, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123138, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123165, "dur": 29, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123213, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123215, "dur": 36, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123253, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123255, "dur": 41, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123297, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123299, "dur": 28, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123329, "dur": 31, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123361, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123363, "dur": 29, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123394, "dur": 27, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123424, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123425, "dur": 31, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123458, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123460, "dur": 28, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123490, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123492, "dur": 26, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123520, "dur": 21, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123543, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123569, "dur": 38, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123609, "dur": 41, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123652, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123653, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123681, "dur": 24, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123708, "dur": 34, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123745, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123776, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123778, "dur": 29, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123809, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123811, "dur": 91, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123905, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123933, "dur": 36, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123972, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165123974, "dur": 33, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124010, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124012, "dur": 31, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124045, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124046, "dur": 25, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124074, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124099, "dur": 23, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124124, "dur": 89, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124217, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124250, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124251, "dur": 26, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124279, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124294, "dur": 82, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124381, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124426, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124428, "dur": 25, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124457, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124534, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124565, "dur": 26, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124593, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124595, "dur": 27, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124623, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124625, "dur": 78, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124707, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124738, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124740, "dur": 27, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124768, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124770, "dur": 82, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124853, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124881, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124908, "dur": 38, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165124951, "dur": 96, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125050, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125090, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125092, "dur": 72, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125172, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125199, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125201, "dur": 46, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125250, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125278, "dur": 61, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125349, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125397, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125399, "dur": 25, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125426, "dur": 84, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125513, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125546, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125552, "dur": 27, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125581, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125582, "dur": 73, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125657, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125688, "dur": 25, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125716, "dur": 25, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125746, "dur": 69, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125818, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125843, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125844, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125869, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125890, "dur": 91, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165125984, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126025, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126026, "dur": 28, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126057, "dur": 15, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126074, "dur": 90, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126166, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126200, "dur": 24, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126227, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126253, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126260, "dur": 83, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126345, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126376, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126403, "dur": 38, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126443, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126444, "dur": 81, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126529, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126558, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126583, "dur": 39, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126626, "dur": 73, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126702, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126744, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126747, "dur": 34, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126783, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126784, "dur": 62, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126850, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126893, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126895, "dur": 28, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126924, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126925, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126954, "dur": 28, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165126985, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127013, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127039, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127069, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127182, "dur": 44, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127228, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127230, "dur": 26, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127260, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127332, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127366, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127369, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127398, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127429, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127453, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127455, "dur": 67, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127527, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127566, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127568, "dur": 34, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127604, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127606, "dur": 26, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127635, "dur": 27, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127664, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127666, "dur": 24, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127694, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127720, "dur": 21, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127744, "dur": 66, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127814, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127842, "dur": 24, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127868, "dur": 28, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127899, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165127979, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128010, "dur": 27, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128038, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128040, "dur": 30, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128071, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128073, "dur": 27, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128103, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128135, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128136, "dur": 23, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128162, "dur": 23, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128189, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128274, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128303, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128336, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128338, "dur": 74, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128415, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128448, "dur": 28, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128479, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128480, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128530, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128539, "dur": 28, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128568, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128569, "dur": 21, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128593, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128619, "dur": 23, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128644, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128669, "dur": 21, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128692, "dur": 80, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128775, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128805, "dur": 32, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128839, "dur": 20, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128862, "dur": 81, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128946, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165128975, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129004, "dur": 81, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129087, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129115, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129116, "dur": 29, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129149, "dur": 27, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129178, "dur": 27, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129208, "dur": 30, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129241, "dur": 29, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129273, "dur": 23, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129299, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129326, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129353, "dur": 70, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129437, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129470, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129471, "dur": 28, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129500, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129502, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129528, "dur": 68, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129598, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129625, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129656, "dur": 22, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129680, "dur": 64, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129747, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129773, "dur": 24, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129800, "dur": 27, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129835, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129836, "dur": 26, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129864, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129865, "dur": 23, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129891, "dur": 36, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129930, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129957, "dur": 24, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165129983, "dur": 70, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130057, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130085, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130115, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130116, "dur": 189, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130319, "dur": 6, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130327, "dur": 218, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130555, "dur": 2, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130561, "dur": 126, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130693, "dur": 7, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130701, "dur": 49, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130753, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130755, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130785, "dur": 77, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130880, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130922, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130925, "dur": 29, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130956, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165130967, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131029, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131066, "dur": 51, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131120, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131147, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131205, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131270, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131305, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131307, "dur": 23, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131335, "dur": 22, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131360, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131438, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131468, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131498, "dur": 24, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131525, "dur": 81, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131607, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131640, "dur": 28, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131671, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131673, "dur": 33, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131708, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131709, "dur": 71, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131782, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131812, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131815, "dur": 23, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131841, "dur": 86, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131929, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131971, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165131975, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132002, "dur": 23, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132028, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132118, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132145, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132179, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132181, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132205, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132281, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132312, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132336, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132364, "dur": 80, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132446, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132478, "dur": 26, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132506, "dur": 22, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132530, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132608, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132640, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132667, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132695, "dur": 84, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132782, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132802, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132825, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132827, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132853, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132876, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132878, "dur": 75, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132955, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165132986, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133013, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133038, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133040, "dur": 79, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133123, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133158, "dur": 29, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133191, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133217, "dur": 78, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133299, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133330, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133332, "dur": 31, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133365, "dur": 22, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133392, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133393, "dur": 84, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133480, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133507, "dur": 29, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133540, "dur": 27, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133569, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133571, "dur": 72, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133646, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133673, "dur": 48, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133724, "dur": 65, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133792, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133851, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133853, "dur": 30, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133886, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133887, "dur": 52, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133942, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165133974, "dur": 28, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134004, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134006, "dur": 22, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134030, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134103, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134131, "dur": 27, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134162, "dur": 23, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134188, "dur": 67, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134258, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134300, "dur": 27, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134328, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134330, "dur": 24, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134356, "dur": 28, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134386, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134387, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134420, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134445, "dur": 24, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134473, "dur": 99, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134574, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134604, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134636, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134662, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134664, "dur": 37, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134703, "dur": 30, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134735, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134737, "dur": 30, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134770, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134771, "dur": 14, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134787, "dur": 22, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134812, "dur": 24, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134839, "dur": 71, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134913, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134974, "dur": 5, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165134980, "dur": 26, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135009, "dur": 24, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135036, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135064, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135066, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135091, "dur": 27, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135121, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135138, "dur": 32, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135174, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135210, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135212, "dur": 74, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135292, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135321, "dur": 179, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135504, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135507, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135569, "dur": 360, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165135932, "dur": 907, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165136844, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165136906, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165136923, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165136966, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165136967, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137021, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137023, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137075, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137078, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137120, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137174, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137178, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137265, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137268, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137375, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137376, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137416, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137417, "dur": 82, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137504, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137506, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137592, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137596, "dur": 47, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137663, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137666, "dur": 106, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137774, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137777, "dur": 47, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165137829, "dur": 170, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138046, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138050, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138221, "dur": 4, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138227, "dur": 44, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138276, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138279, "dur": 118, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138435, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138439, "dur": 53, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138496, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165138501, "dur": 461, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139078, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139082, "dur": 97, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139182, "dur": 10, "ph": "X", "name": "ProcessMessages 2320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139193, "dur": 47, "ph": "X", "name": "ReadAsync 2320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139244, "dur": 27, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139274, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139326, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139329, "dur": 73, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139404, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139407, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139473, "dur": 11, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139487, "dur": 78, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139569, "dur": 3, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139574, "dur": 90, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139667, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139670, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139703, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139706, "dur": 62, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139772, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139781, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139824, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139828, "dur": 132, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139964, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165139967, "dur": 111, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140081, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140084, "dur": 45, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140132, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140134, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140305, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165140333, "dur": 13464, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153804, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153808, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153845, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153848, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153914, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153916, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165153956, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154044, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154073, "dur": 617, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154694, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154736, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154767, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154796, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154876, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165154906, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155200, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155234, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155236, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155278, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155473, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155526, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155529, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155568, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155571, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155616, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155618, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155650, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155653, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155693, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155696, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155725, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155840, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155870, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165155872, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156281, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156314, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156316, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156352, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156379, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156381, "dur": 182, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156567, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156600, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156602, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156642, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156644, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156676, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156678, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156733, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156769, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156771, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156803, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156805, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156837, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156865, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156866, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156893, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165156895, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157014, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157044, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157074, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157076, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157112, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157114, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157142, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157145, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157201, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157229, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157278, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157319, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157321, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157360, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157362, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157398, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157419, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157454, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157483, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157541, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157571, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157647, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157687, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157689, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157723, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157754, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157757, "dur": 63, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157824, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157856, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157902, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157941, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157979, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165157981, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158020, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158063, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158066, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158098, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158100, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158138, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158140, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158220, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158252, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158254, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158302, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158338, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158377, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158412, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158481, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158518, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158552, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158614, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158649, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158684, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158723, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158725, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158762, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158764, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158794, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158858, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158891, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158923, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158956, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158958, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165158998, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159031, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159057, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159086, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159181, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159207, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159233, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159263, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159437, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159502, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159537, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159540, "dur": 348, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159892, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159919, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159959, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165159983, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160026, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160027, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160069, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160071, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160104, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160106, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160169, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160200, "dur": 542, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160744, "dur": 50, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160798, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160801, "dur": 71, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160876, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165160905, "dur": 188, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161097, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161126, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161179, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161213, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161253, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161326, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161362, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161388, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161418, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161445, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161586, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161657, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161748, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161791, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165161794, "dur": 350, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162205, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162246, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162247, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162279, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162310, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162338, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162373, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162375, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162409, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162573, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162614, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162672, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165162705, "dur": 924, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163633, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163666, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163669, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163715, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163896, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163930, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165163932, "dur": 522, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165164459, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165164494, "dur": 1181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165165679, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165165681, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165165713, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165165716, "dur": 73030, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165238754, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165238758, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165238806, "dur": 1810, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165240620, "dur": 7525, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248154, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248158, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248218, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248220, "dur": 50, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248275, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248329, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248331, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248369, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248397, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248399, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248429, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248432, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248596, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248642, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248739, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248775, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165248777, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165249160, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165249191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165249193, "dur": 1419, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250616, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250647, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250649, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250687, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250718, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250753, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250786, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250859, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250890, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250928, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250930, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250963, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165250965, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251111, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251145, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251146, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251181, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251208, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251457, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251487, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251584, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165251612, "dur": 1064, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165252680, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165252710, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253118, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253146, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253148, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253187, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253216, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253252, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253315, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253344, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253346, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253416, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253447, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253449, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253484, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253486, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253517, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253520, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253758, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253783, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253883, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253911, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165253913, "dur": 593, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254511, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254540, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254579, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254581, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254617, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254666, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254705, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254707, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254736, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254738, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254857, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254891, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254893, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254926, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254929, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254948, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254979, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165254981, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255015, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255049, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255081, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255083, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255338, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255368, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255370, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255450, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255479, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255480, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255511, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255513, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255542, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255543, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255662, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255691, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255716, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255749, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255751, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255779, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255781, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255820, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255823, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255860, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255891, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255927, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255929, "dur": 29, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255962, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255964, "dur": 29, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255996, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165255998, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256032, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256034, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256072, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256075, "dur": 26, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256103, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256104, "dur": 31, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256137, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256140, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256178, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256180, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256221, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256223, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256265, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256267, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256302, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256434, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256480, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256482, "dur": 36, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256521, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256522, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256599, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256632, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256634, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165256663, "dur": 76780, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165333453, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165333458, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165333493, "dur": 22, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165333517, "dur": 11693, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165345219, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165345258, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165345304, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165345306, "dur": 17138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362453, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362457, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362484, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362487, "dur": 157, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362651, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362701, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165362703, "dur": 79853, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442566, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442570, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442627, "dur": 28, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442657, "dur": 152, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442814, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442857, "dur": 16, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165442874, "dur": 24173, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165467058, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165467062, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165467089, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165467093, "dur": 145, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165467242, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165467281, "dur": 1277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165468562, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165468596, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165468622, "dur": 3425, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472054, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472057, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472101, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472105, "dur": 498, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472609, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472658, "dur": 21, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165472679, "dur": 555, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165473239, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165473279, "dur": 352, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749045165473635, "dur": 8067, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749045165495825, "dur": 1586, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5696, "tid": 8589934592, "ts": 1749045165075018, "dur": 133346, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749045165208367, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749045165208372, "dur": 1078, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749045165497413, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5696, "tid": 4294967296, "ts": 1749045165056522, "dur": 426227, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749045165059996, "dur": 9056, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749045165482809, "dur": 4988, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749045165486274, "dur": 94, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749045165487877, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749045165497420, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749045165105948, "dur": 1987, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165107948, "dur": 1409, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165109486, "dur": 70, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749045165109556, "dur": 454, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165110742, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749045165111827, "dur": 2223, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749045165130784, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1749045165131560, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1749045165135264, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1749045165110033, "dur": 25627, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165135675, "dur": 337359, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165473035, "dur": 134, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165473169, "dur": 145, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165473587, "dur": 1406, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749045165110253, "dur": 25455, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165135729, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165135846, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165136778, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165137412, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165137672, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_5E20C3D8D90E43EC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165137750, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165137893, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165137967, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165138225, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165138340, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_15E32D3DB1F12CD0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165138432, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165138654, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_22FDE6BD161A4064.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165138715, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165138781, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749045165138995, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749045165139330, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165139472, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749045165139694, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749045165139892, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165140070, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16372163617230684214.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749045165140308, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165141187, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165143176, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165144042, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165144313, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165144563, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165145194, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165145398, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165145927, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165146175, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165146384, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165147319, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165147606, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165148722, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165148975, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165149262, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165150069, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165151061, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165152251, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165152470, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165152677, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165152883, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165153610, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165154228, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165155105, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165155647, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165155938, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749045165156759, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165156964, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165157032, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165157147, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165157231, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165157640, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165158102, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749045165158291, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749045165158966, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165159141, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749045165159779, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165159864, "dur": 4299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165164164, "dur": 81791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165245957, "dur": 2514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749045165248472, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165248720, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749045165251191, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165251458, "dur": 4174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749045165255632, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165255867, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165256517, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749045165256619, "dur": 216409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165110378, "dur": 25409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165135795, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165136887, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165137232, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7AD711F5F88D031D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165137287, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165137517, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165137782, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165137971, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F21D3FF6DC4634B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165138068, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_6241537ACFF4D8B0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165138308, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4A1C632ABDE44C9A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165138360, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165138829, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749045165139045, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749045165139164, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165139234, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1749045165139449, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749045165140067, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6572595573279557027.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749045165140243, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749045165140325, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165140829, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165142440, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165142640, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165142862, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165143107, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165143791, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165144035, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165144253, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165144500, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165144735, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165145830, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165146399, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165147385, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165147613, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165149263, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165150151, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165150987, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165151965, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165152162, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165152409, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165152661, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165153951, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165154232, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165155108, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165155628, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165155976, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165156592, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165156739, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165157019, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165157224, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165157463, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165158004, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165158085, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165158253, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165158473, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165158986, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165159085, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165159351, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165159643, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165161099, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165161241, "dur": 1306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165162548, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165162709, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165162925, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165163884, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165164077, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749045165164268, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165164702, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165164808, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165165923, "dur": 84, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165166744, "dur": 167010, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165338654, "dur": 6658, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749045165338065, "dur": 7328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165345394, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165345526, "dur": 13925, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749045165345522, "dur": 15460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165362301, "dur": 178, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749045165363292, "dur": 79876, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749045165467225, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749045165467216, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749045165467360, "dur": 5710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165110263, "dur": 25459, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165135730, "dur": 1165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165136895, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165137220, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_3359014152B0440F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165137316, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165137441, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165137706, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165137884, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_50E1A15E13819776.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165137938, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165138056, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165138308, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165138493, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165138832, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749045165139075, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749045165139279, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165139487, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749045165139762, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165139903, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749045165140108, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749045165140387, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165141670, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165144609, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Editor\\2D\\ShapeEditor\\Shapes\\Spline.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749045165143359, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165145303, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165145862, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165146071, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165146293, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165146500, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165146755, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165146966, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165147174, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165147446, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165147657, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165149078, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165149271, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165150198, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165150905, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165151670, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165152775, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165153159, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165154235, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165155101, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165155648, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165155977, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165156048, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165157033, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165157234, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165157471, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165157676, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165157770, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165158279, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165158385, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749045165158587, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165158832, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165159216, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165159335, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1749045165159887, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165160040, "dur": 154, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165160555, "dur": 78525, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1749045165245944, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165248411, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165248504, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165251007, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165251117, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165253404, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165253476, "dur": 2539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749045165256016, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165256107, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165256547, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749045165256791, "dur": 216405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165110367, "dur": 25405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165135783, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165136763, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165137186, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3DE1FE7E9BAAF7A9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165137248, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165137401, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165137461, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165137697, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165137874, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165137983, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165138110, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165138413, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165138540, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749045165138830, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749045165139003, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749045165139179, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165139470, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749045165139726, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749045165140112, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749045165140333, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165140953, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165142240, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165142947, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165143601, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165144627, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165145292, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165145787, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165147380, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165147631, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165148111, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165148318, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165148557, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165148866, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165149709, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165150413, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165150875, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165152009, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165152289, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165152512, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165152998, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165153090, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165154211, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165155080, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165155622, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165155888, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749045165156440, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165156729, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165156921, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749045165157598, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165157722, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165157809, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165157995, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749045165158537, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165159018, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165159345, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165159672, "dur": 4398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165164072, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749045165164246, "dur": 81716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165245973, "dur": 2735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749045165248765, "dur": 2804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749045165251571, "dur": 1456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165253037, "dur": 2459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749045165255497, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165255889, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165256248, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165256520, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749045165256581, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749045165256981, "dur": 216125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165110321, "dur": 25425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165135753, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165136902, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165137239, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_774893DE4F1F8941.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165137293, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165137518, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165137792, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165137978, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_42CC435405CEA14C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165138052, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165138217, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165138438, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165138543, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6EBD506B2644EFF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165138880, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165139302, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749045165139488, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749045165140006, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749045165140146, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749045165140339, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165141713, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165143036, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165143745, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165143984, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165144197, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165144490, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165144699, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165145582, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165146700, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165146945, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165147154, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165147360, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165147594, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165148034, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165148201, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165148402, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165148704, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165148936, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165149467, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165150499, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165151258, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165152184, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165152509, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165152796, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165153121, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165154230, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165155095, "dur": 861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165155957, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165156770, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165157214, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749045165157415, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165157948, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165158170, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165158392, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165158964, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165159082, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165159334, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165159648, "dur": 4429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165164077, "dur": 50312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165214389, "dur": 31588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165245978, "dur": 2522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165248501, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165248684, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165250913, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165251041, "dur": 2404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165253446, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165253513, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749045165256010, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165256520, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165256617, "dur": 210615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749045165467254, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749045165467233, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749045165467453, "dur": 1463, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749045165468918, "dur": 4125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165110446, "dur": 25368, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165135824, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165136795, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165137269, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_DE30F0D9293C6825.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165137321, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165137487, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DDBE5B8EC557ADE5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165137545, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165137870, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_84BF5D8C55DA5D3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165137964, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B0964A0CA2EEEE2D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165138059, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165138257, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_5D725690D6CA2AA1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165138314, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165138591, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165138790, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749045165138855, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165139484, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749045165139723, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749045165139796, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749045165139883, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165140036, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749045165140132, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749045165140292, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165140977, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165142321, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165142527, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165142725, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165142942, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165143838, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165144049, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165144355, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165144578, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165145303, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165145743, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165146873, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165147083, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165147310, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165147529, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165147871, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2c53672ff4c2\\Runtime\\Common\\Swap.Extensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749045165147755, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165149090, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165149333, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165150287, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165151199, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165152220, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165152431, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165152658, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165154000, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165154234, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165155097, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165155651, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165155894, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165155979, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165156589, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165156719, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165156923, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165157550, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165157665, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165158111, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165158387, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165158593, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165158732, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165159139, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165159246, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165159340, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165159647, "dur": 2143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165161791, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749045165161938, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165162402, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165162533, "dur": 1556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165164089, "dur": 81864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165245954, "dur": 2487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165248442, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165249096, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165251578, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165251817, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749045165254389, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165255007, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165255067, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165255413, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165255695, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165256328, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165256542, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749045165256784, "dur": 216382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165110301, "dur": 25432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165135740, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165136862, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165137257, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165137433, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B4FC9B590A58509F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165137486, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165137727, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5E49D6BEFCDFF46D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165137779, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165138065, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165138351, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165138505, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165138741, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165139099, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165139233, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749045165139465, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749045165139663, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749045165139798, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1749045165140000, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749045165140090, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749045165140321, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165140910, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165142343, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165142555, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165142762, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165142941, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165144204, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165144526, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165145221, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165145569, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165147056, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165147305, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165147531, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165147758, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165149615, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165150409, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165150988, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@e2c83221d2dc\\InputSystem\\Editor\\PropertyDrawers\\InputActionDrawer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749045165150988, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165152376, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165152662, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165153616, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165154221, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165155079, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165155623, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165155865, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165156553, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165156641, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165156698, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165157441, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165158100, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165158305, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165158819, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165159113, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165159338, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165159633, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165159833, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165160328, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165160449, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165160637, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165161361, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165161458, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165161526, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749045165161689, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165162013, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165162126, "dur": 1973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165164100, "dur": 81827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165245929, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165248509, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165248624, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165251176, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165251246, "dur": 2483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165253730, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165253809, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749045165256510, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165256596, "dur": 210621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749045165467232, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749045165467219, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749045165467588, "dur": 5470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165110355, "dur": 25403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165135766, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165136929, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165137327, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165137467, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B4A1BF472DE35F91.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165137531, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165137777, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165137979, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B7E1353FAF528495.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165138050, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_DA6971D49F67486A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165138303, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165138409, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_B5974478C0B40EC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165138599, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165139252, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749045165139476, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749045165139684, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749045165139763, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165140042, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749045165140335, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165141322, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165142679, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165142887, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165143798, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165144017, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165144306, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165144517, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165144739, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165145551, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165146682, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165146926, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165147183, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165147408, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165147615, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165148601, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165148854, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165149172, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165150028, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165150757, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165152229, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165152502, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165152739, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165153089, "dur": 1124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165154213, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165155098, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165155670, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165155923, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165156198, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749045165156877, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165157042, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_7F2C410A434F5518.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165157124, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165157409, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165158111, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165158401, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165159320, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165159641, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749045165159781, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749045165160152, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165160265, "dur": 3889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165164154, "dur": 81787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165245942, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749045165248442, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165248538, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749045165250948, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165251279, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749045165253768, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165254115, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749045165256739, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749045165256836, "dur": 216285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165110252, "dur": 25436, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165135720, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165135857, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165136881, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165137319, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165137370, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165137662, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7FFEF131F3AD3BA4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165137717, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165137922, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_774CD8E5A270CA86.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165137973, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165138335, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165138490, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165139485, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749045165139699, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749045165139880, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165139960, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749045165140026, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749045165140329, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165140950, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165142113, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165143014, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165143640, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165145445, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165145921, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165146133, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165146359, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165146920, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165147188, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165147467, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165147699, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165148127, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165148333, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165148589, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165148863, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165149529, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165150592, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165151674, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165152870, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165153141, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165154210, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165155083, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165155620, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165155822, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165157680, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165158006, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165158104, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165158234, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165158328, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165159436, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165159538, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165160855, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165160960, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749045165161123, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165161583, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165161714, "dur": 2395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165164110, "dur": 81828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165245940, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165248360, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165248466, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165250949, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165251066, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165253460, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165253526, "dur": 2667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749045165256194, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165256345, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165256416, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749045165256561, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749045165256828, "dur": 216307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165110416, "dur": 25380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165135807, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165136855, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165137217, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_06983A7F9FE1EEBF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165137300, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165137413, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_326BFA302771D2EF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165137465, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165137675, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165137856, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165137978, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9C249CA42F8C791A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165138073, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165138231, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165138336, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B1A10C5722B27484.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165138387, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165138738, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_78BD15807B404F33.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165138998, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749045165139478, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749045165139695, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749045165139901, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749045165140002, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749045165140353, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165142090, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165142456, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165142687, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165142873, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165143328, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165144507, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165144715, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165145652, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165146603, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165146825, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165147027, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165147243, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165147475, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165147793, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2c53672ff4c2\\Runtime\\Debugging\\Prefabs\\Scripts\\UIFoldout.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749045165147680, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165149014, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165149228, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165149762, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165150554, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165152149, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165152392, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165152659, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165153876, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165154243, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165155106, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165155643, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165155895, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165157513, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165157713, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165157895, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165158440, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165158622, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165159321, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165159639, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749045165159831, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165160295, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165160446, "dur": 3679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165164126, "dur": 81797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165245927, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165248361, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165248464, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165250784, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165251315, "dur": 2442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165253757, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165253848, "dur": 2912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749045165256761, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749045165256838, "dur": 216251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165110468, "dur": 25363, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165135841, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165136917, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165137223, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_6E9570E2A3C5474A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165137302, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165137478, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165137535, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165137872, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165137973, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_ADF67561AEE86815.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165138062, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165138250, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165138307, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165138434, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165138617, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165139013, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749045165139145, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749045165139488, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749045165139763, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165139869, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165140033, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749045165140383, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165142265, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165142495, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165142725, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165142913, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165143711, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165144056, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165144368, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165144622, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165145256, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165145793, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165146481, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165146717, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165146953, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165147192, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165147444, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165147658, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2c53672ff4c2\\Runtime\\Lighting\\ProbeVolume\\ProbeVolumeGIContributor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749045165147657, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165149612, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165150527, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165151265, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165151931, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165152904, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165153892, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165154229, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165155096, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165155618, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165155828, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165157346, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165157470, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165157643, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165158263, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165158383, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165158591, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165159487, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165159643, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165159844, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165160249, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165160345, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165160512, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165160996, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165161094, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749045165161223, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165161509, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165161595, "dur": 2522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165164118, "dur": 81841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165245963, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165248324, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165248486, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165250867, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165250974, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165253029, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165253574, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165256294, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165256521, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749045165256592, "dur": 81480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165338107, "dur": 21342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749045165338074, "dur": 22908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165362741, "dur": 220, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749045165363414, "dur": 79494, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749045165467222, "dur": 5134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749045165467211, "dur": 5147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749045165472376, "dur": 585, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749045165110502, "dur": 25345, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165135858, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749045165136813, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165137355, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165137582, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165137953, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165138355, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165138500, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165138607, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4D1C445C9CC0E084.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749045165138667, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749045165138916, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749045165139204, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165139470, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749045165139620, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749045165139815, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749045165139875, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165140157, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749045165140296, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165141103, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165142791, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165143021, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165143710, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165144273, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165144525, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165144744, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165145366, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165145810, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165146690, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165146918, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165147116, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165147328, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165147555, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165147788, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165147965, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165148105, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165148244, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165148400, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165148603, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165148853, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165149060, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165149269, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165150323, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165151387, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165152338, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165152562, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165154088, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165154248, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165155086, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165155638, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749045165155890, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749045165156516, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165157044, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165157120, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165157222, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165157404, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165157482, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749045165158487, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165158626, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165159344, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165159663, "dur": 4410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165164075, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749045165164257, "dur": 81692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165245960, "dur": 2743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749045165248704, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165248771, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749045165251153, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165251219, "dur": 2440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749045165253660, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165253774, "dur": 2916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749045165256699, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749045165256778, "dur": 216251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165110535, "dur": 25327, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165135873, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165136829, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165137291, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_13ED6B36C70FAB0A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165137345, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165137579, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165137864, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165138000, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165138248, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165138305, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165138475, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_95CE26B7D8385BCA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165138545, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165138617, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_95CE26B7D8385BCA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165138739, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165138912, "dur": 15195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165154109, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165154275, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165154397, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165155128, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165155230, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165155640, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165155819, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165156440, "dur": 698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165157209, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165157437, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165157963, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165158108, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165158335, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165158432, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165158850, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165158961, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165159334, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165159642, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165160965, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165161119, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165161687, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165161786, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165161932, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165162511, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165162607, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749045165162728, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165163049, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165164080, "dur": 81850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165245946, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165248418, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165248944, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165251450, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165251537, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165253816, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749045165254240, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749045165256967, "dur": 216109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165110562, "dur": 25318, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165135889, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165136823, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165137340, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165137411, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165137701, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165137889, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_98C9D7CFA3055094.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165137943, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165138054, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165138204, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165138275, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165138342, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F4B95EF39F1A8010.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165138969, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749045165139475, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749045165139839, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749045165140021, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165140093, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749045165140303, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165140935, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165142012, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165143172, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165143778, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165144071, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165144338, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165144562, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165145233, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165145826, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165146895, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165147110, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165147394, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165147612, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165148511, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165148775, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165149029, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165149228, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165149718, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165150301, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165150966, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@e2c83221d2dc\\InputSystem\\Editor\\UITKAssetEditor\\Views\\MatchingControlPaths.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749045165150966, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165152304, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165152539, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165153841, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165154227, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165155093, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165155638, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165155848, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165155970, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749045165156851, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165157014, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165157278, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165157633, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165158108, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165158400, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165159319, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165159442, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749045165159577, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749045165160543, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165160622, "dur": 3512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165164134, "dur": 81802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165245938, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749045165248428, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165248516, "dur": 2415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749045165250932, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165250998, "dur": 2500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749045165253499, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165253669, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749045165256508, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749045165256629, "dur": 216402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165110584, "dur": 25311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165135906, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165136922, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165137328, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165137527, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165137882, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165138073, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165138222, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165138327, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_D77C7CCB1D31CDA5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165138388, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165138451, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E79D22299C4825D4.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165138503, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165138653, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749045165138787, "dur": 410, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749045165139509, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749045165139926, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749045165140064, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749045165140199, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749045165140422, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165140617, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165141904, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165143500, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165144521, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165144742, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165145403, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165145889, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165146104, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165146351, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165147168, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165147457, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165147685, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165149118, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165149658, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165150805, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165151953, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165152871, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165153225, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165154236, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165155104, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165155654, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165155884, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165156230, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165156611, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749045165157325, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165157559, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165157632, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165158118, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165158389, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165158646, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749045165159141, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165159264, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165159322, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165159635, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749045165159841, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749045165160309, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165160440, "dur": 3704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165164144, "dur": 81802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165245959, "dur": 2925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749045165248885, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165249512, "dur": 2290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749045165251803, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165251946, "dur": 4182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749045165256129, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165256244, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165256435, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165256551, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749045165256826, "dur": 216327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165110609, "dur": 25305, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165135915, "dur": 994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749045165136909, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165137325, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165137490, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9DF987BA9AE65833.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749045165137552, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165137892, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165138065, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165138287, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165138446, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_0EF0635D76D99CD0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749045165138497, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165138649, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165138829, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749045165138994, "dur": 15051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165154047, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165154216, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165155081, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165155633, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749045165155901, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165156861, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165157036, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165157130, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749045165157309, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165157408, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165158129, "dur": 725, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165158896, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165159342, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165159651, "dur": 4424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165164075, "dur": 45748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165212978, "dur": 238, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 16, "ts": 1749045165213217, "dur": 1084, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 16, "ts": 1749045165214302, "dur": 75, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 16, "ts": 1749045165209824, "dur": 4559, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165214384, "dur": 31550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165245937, "dur": 2754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165248692, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165248756, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165251196, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165251292, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165253593, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165253686, "dur": 2992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749045165256679, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749045165256780, "dur": 216400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749045165480170, "dur": 1517, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5696, "tid": 44, "ts": 1749045165498005, "dur": 3122, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5696, "tid": 44, "ts": 1749045165501161, "dur": 1208, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749045165493783, "dur": 9524, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}