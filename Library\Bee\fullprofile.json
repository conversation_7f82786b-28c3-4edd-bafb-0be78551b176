{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5696, "tid": 41, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5696, "tid": 41, "ts": 1749046424378442, "dur": 917, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5696, "tid": 41, "ts": 1749046424383182, "dur": 773, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5696, "tid": 1, "ts": 1749046422812417, "dur": 15934, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749046422828356, "dur": 66872, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749046422895238, "dur": 48573, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5696, "tid": 41, "ts": 1749046424383958, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 5696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422810600, "dur": 32050, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422842653, "dur": 1523450, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422843538, "dur": 2510, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422846057, "dur": 1662, "ph": "X", "name": "ProcessMessages 11441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422847722, "dur": 239, "ph": "X", "name": "ReadAsync 11441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422847963, "dur": 10, "ph": "X", "name": "ProcessMessages 20510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422847974, "dur": 38, "ph": "X", "name": "ReadAsync 20510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848017, "dur": 37, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848057, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848094, "dur": 31, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848127, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848129, "dur": 31, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848162, "dur": 32, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848197, "dur": 28, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848228, "dur": 30, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848261, "dur": 26, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848290, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848316, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848342, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848367, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848390, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848419, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848421, "dur": 31, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848455, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848485, "dur": 36, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848524, "dur": 25, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848551, "dur": 31, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848585, "dur": 31, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848619, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848660, "dur": 30, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848692, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848693, "dur": 40, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848735, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848762, "dur": 41, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848805, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848807, "dur": 35, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848844, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848845, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848875, "dur": 27, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848905, "dur": 27, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848934, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848935, "dur": 24, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422848961, "dur": 81, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849046, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849076, "dur": 22, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849100, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849128, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849157, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849182, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849184, "dur": 34, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849221, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849266, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849269, "dur": 25, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849297, "dur": 29, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849329, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849352, "dur": 34, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849389, "dur": 32, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849433, "dur": 28, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849463, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849465, "dur": 20, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849487, "dur": 25, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849515, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849542, "dur": 36, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849583, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849622, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849624, "dur": 39, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849667, "dur": 36, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849704, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849706, "dur": 31, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849739, "dur": 23, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849765, "dur": 26, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849793, "dur": 26, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849822, "dur": 47, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849872, "dur": 28, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849901, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849902, "dur": 20, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849925, "dur": 22, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849950, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422849976, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850005, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850007, "dur": 31, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850040, "dur": 24, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850067, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850068, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850091, "dur": 22, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850115, "dur": 26, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850144, "dur": 23, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850169, "dur": 25, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850196, "dur": 27, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850226, "dur": 29, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850257, "dur": 23, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850283, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850316, "dur": 28, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850348, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850383, "dur": 25, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850410, "dur": 28, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850439, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850440, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850480, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850507, "dur": 40, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850549, "dur": 21, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850573, "dur": 28, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850603, "dur": 26, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850631, "dur": 34, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850669, "dur": 23, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850695, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850728, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850731, "dur": 27, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850761, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850788, "dur": 27, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850818, "dur": 21, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850841, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850871, "dur": 32, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850905, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850907, "dur": 23, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850933, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850961, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422850989, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851015, "dur": 27, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851043, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851045, "dur": 29, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851084, "dur": 29, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851116, "dur": 24, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851142, "dur": 29, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851174, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851202, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851204, "dur": 27, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851233, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851235, "dur": 32, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851268, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851270, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851300, "dur": 29, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851332, "dur": 26, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851361, "dur": 23, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851386, "dur": 26, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851415, "dur": 13, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851430, "dur": 72, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851505, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851540, "dur": 23, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851566, "dur": 29, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851598, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851599, "dur": 30, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851633, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851680, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851682, "dur": 128, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851812, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851844, "dur": 26, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851872, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851873, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851902, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851928, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851954, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422851978, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852006, "dur": 31, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852039, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852041, "dur": 33, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852077, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852078, "dur": 24, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852104, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852127, "dur": 30, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852161, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852198, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852200, "dur": 31, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852233, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852235, "dur": 25, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852262, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852286, "dur": 35, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852324, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852353, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852355, "dur": 24, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852381, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852382, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852408, "dur": 21, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852432, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852458, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852481, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852506, "dur": 24, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852533, "dur": 23, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852558, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852560, "dur": 32, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852596, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852624, "dur": 28, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852656, "dur": 27, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852685, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852710, "dur": 31, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852744, "dur": 24, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852770, "dur": 26, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852799, "dur": 28, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852828, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852830, "dur": 31, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852865, "dur": 32, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852900, "dur": 26, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852929, "dur": 28, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852959, "dur": 29, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852990, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422852993, "dur": 32, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853027, "dur": 22, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853053, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853077, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853102, "dur": 23, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853128, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853153, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853155, "dur": 23, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853181, "dur": 35, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853218, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853220, "dur": 25, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853248, "dur": 23, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853274, "dur": 24, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853300, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853333, "dur": 26, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853363, "dur": 26, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853391, "dur": 24, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853418, "dur": 28, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853449, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853470, "dur": 29, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853502, "dur": 25, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853530, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853560, "dur": 23, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853584, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853611, "dur": 30, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853644, "dur": 30, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853677, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853679, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853709, "dur": 19, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853730, "dur": 22, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853756, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853788, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853789, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853815, "dur": 25, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853842, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853843, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853868, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853896, "dur": 23, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853922, "dur": 27, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853951, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422853976, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854002, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854031, "dur": 27, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854061, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854063, "dur": 33, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854098, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854100, "dur": 28, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854131, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854133, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854155, "dur": 23, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854182, "dur": 25, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854210, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854235, "dur": 31, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854269, "dur": 29, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854300, "dur": 26, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854329, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854356, "dur": 21, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854380, "dur": 30, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854412, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854439, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854440, "dur": 33, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854476, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854478, "dur": 29, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854510, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854536, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854564, "dur": 26, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854594, "dur": 25, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854621, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854652, "dur": 27, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854682, "dur": 28, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854713, "dur": 25, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854740, "dur": 30, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854772, "dur": 28, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854803, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854830, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854832, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854873, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854875, "dur": 27, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854905, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854906, "dur": 33, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854943, "dur": 35, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854981, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422854983, "dur": 29, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855015, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855016, "dur": 40, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855058, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855060, "dur": 27, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855089, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855119, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855164, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855208, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855210, "dur": 36, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855249, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855251, "dur": 25, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855280, "dur": 27, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855308, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855310, "dur": 24, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855338, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855373, "dur": 26, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855400, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855402, "dur": 31, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855435, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855437, "dur": 30, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855469, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855470, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855494, "dur": 22, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855521, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855550, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855552, "dur": 29, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855585, "dur": 25, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855613, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855636, "dur": 23, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855662, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855694, "dur": 33, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855730, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855732, "dur": 33, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855767, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855769, "dur": 21, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855792, "dur": 24, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855819, "dur": 32, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855854, "dur": 24, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855880, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855881, "dur": 25, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855909, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855932, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855962, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855994, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422855995, "dur": 26, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856024, "dur": 25, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856067, "dur": 30, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856099, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856102, "dur": 32, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856136, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856137, "dur": 26, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856167, "dur": 25, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856194, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856195, "dur": 29, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856226, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856227, "dur": 24, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856253, "dur": 28, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856283, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856285, "dur": 24, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856312, "dur": 33, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856348, "dur": 24, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856376, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856400, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856402, "dur": 41, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856445, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856447, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856477, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856479, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856505, "dur": 31, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856538, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856539, "dur": 23, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856566, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856591, "dur": 26, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856620, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856645, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856670, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856706, "dur": 32, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856740, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856769, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856798, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856827, "dur": 35, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856866, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856896, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856897, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856927, "dur": 31, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856961, "dur": 29, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856993, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422856996, "dur": 34, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857033, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857035, "dur": 31, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857068, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857071, "dur": 43, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857117, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857119, "dur": 41, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857163, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857165, "dur": 34, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857201, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857202, "dur": 29, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857234, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857235, "dur": 32, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857271, "dur": 25, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857300, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857332, "dur": 25, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857359, "dur": 25, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857387, "dur": 26, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857416, "dur": 22, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857441, "dur": 25, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857469, "dur": 35, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857507, "dur": 29, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857538, "dur": 25, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857566, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857593, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857618, "dur": 29, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857649, "dur": 2, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857652, "dur": 24, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857679, "dur": 34, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857714, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857717, "dur": 32, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857751, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857752, "dur": 35, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857789, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857791, "dur": 33, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857826, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857827, "dur": 26, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857856, "dur": 30, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857889, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857891, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857923, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857925, "dur": 26, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857955, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857983, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422857985, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858010, "dur": 30, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858041, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858043, "dur": 29, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858075, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858096, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858128, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858130, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858148, "dur": 30, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858182, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858217, "dur": 39, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858258, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858285, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858310, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858312, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858336, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858339, "dur": 89, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858430, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858462, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858489, "dur": 24, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858515, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858541, "dur": 26, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858570, "dur": 23, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858596, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858619, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858645, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858648, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858732, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858735, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858769, "dur": 50, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858821, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858823, "dur": 26, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858851, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858854, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858932, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858987, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422858988, "dur": 29, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859019, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859021, "dur": 84, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859108, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859139, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859167, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859195, "dur": 92, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859291, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859320, "dur": 25, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859349, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859374, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859455, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859490, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859518, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859544, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859622, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859653, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859682, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859706, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859782, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859811, "dur": 23, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859835, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859836, "dur": 23, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859862, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859942, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859963, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422859991, "dur": 25, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860018, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860019, "dur": 72, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860093, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860126, "dur": 21, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860150, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860178, "dur": 79, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860261, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860288, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860311, "dur": 23, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860336, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860362, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860364, "dur": 71, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860437, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860465, "dur": 23, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860490, "dur": 24, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860517, "dur": 85, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860604, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860640, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860641, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860673, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860701, "dur": 23, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860727, "dur": 101, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860831, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860906, "dur": 36, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860946, "dur": 27, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422860976, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861001, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861098, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861138, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861141, "dur": 29, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861172, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861174, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861201, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861288, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861322, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861324, "dur": 34, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861360, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861362, "dur": 26, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861392, "dur": 80, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861475, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861519, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861523, "dur": 40, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861565, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861568, "dur": 97, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861671, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861708, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861713, "dur": 47, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861763, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861766, "dur": 47, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861815, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861817, "dur": 46, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861867, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861869, "dur": 43, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861914, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861915, "dur": 41, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861959, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861962, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861996, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422861998, "dur": 75, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862079, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862131, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862132, "dur": 30, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862164, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862166, "dur": 85, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862254, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862285, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862287, "dur": 31, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862320, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862322, "dur": 38, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862362, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862364, "dur": 27, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862394, "dur": 68, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862467, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862496, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862498, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862528, "dur": 28, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862559, "dur": 27, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862589, "dur": 25, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862617, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862644, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862673, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862758, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862788, "dur": 26, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862817, "dur": 25, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862843, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862846, "dur": 72, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862920, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862952, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422862980, "dur": 29, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863012, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863014, "dur": 24, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863041, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863066, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863068, "dur": 26, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863098, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863124, "dur": 23, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863150, "dur": 69, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863221, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863248, "dur": 25, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863276, "dur": 26, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863305, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863379, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863408, "dur": 28, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863438, "dur": 22, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863464, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863493, "dur": 27, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863523, "dur": 25, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863550, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863551, "dur": 24, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863577, "dur": 25, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863604, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863606, "dur": 74, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863683, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863711, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863714, "dur": 24, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863741, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863766, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863768, "dur": 71, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863842, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863870, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863898, "dur": 22, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863924, "dur": 68, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422863994, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864025, "dur": 31, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864059, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864085, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864087, "dur": 41, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864130, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864131, "dur": 29, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864163, "dur": 2, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864166, "dur": 25, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864193, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864195, "dur": 26, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864223, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864249, "dur": 92, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864345, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864383, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864386, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864417, "dur": 81, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864502, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864536, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864537, "dur": 31, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864570, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864571, "dur": 24, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864598, "dur": 78, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864679, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864707, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864732, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864763, "dur": 25, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864791, "dur": 28, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864824, "dur": 22, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864848, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864874, "dur": 85, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864962, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422864988, "dur": 30, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865021, "dur": 22, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865046, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865048, "dur": 77, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865127, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865156, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865181, "dur": 32, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865217, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865255, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865257, "dur": 25, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865285, "dur": 91, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865380, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865417, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865419, "dur": 34, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865457, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865462, "dur": 70, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865536, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865570, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865572, "dur": 33, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865607, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865609, "dur": 82, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865695, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865728, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865730, "dur": 23, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865755, "dur": 27, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865785, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865787, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865870, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865896, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865923, "dur": 27, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865954, "dur": 38, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865994, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422865996, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866056, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866084, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866086, "dur": 27, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866115, "dur": 80, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866199, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866230, "dur": 24, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866257, "dur": 25, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866285, "dur": 82, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866370, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866405, "dur": 21, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866428, "dur": 24, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866455, "dur": 78, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866538, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866575, "dur": 33, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866611, "dur": 32, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866645, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866646, "dur": 62, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866711, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866738, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866740, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866774, "dur": 24, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866800, "dur": 78, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866881, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866912, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866914, "dur": 27, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866943, "dur": 25, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422866971, "dur": 78, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867052, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867080, "dur": 33, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867115, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867117, "dur": 30, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867149, "dur": 72, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867224, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867253, "dur": 26, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867282, "dur": 86, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867372, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867402, "dur": 26, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867432, "dur": 77, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867513, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867557, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867559, "dur": 33, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867594, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867596, "dur": 79, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867679, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867709, "dur": 32, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867743, "dur": 24, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867770, "dur": 71, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867843, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867872, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867875, "dur": 25, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867903, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422867930, "dur": 73, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868005, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868039, "dur": 24, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868065, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868066, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868100, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868101, "dur": 83, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868189, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868220, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868254, "dur": 25, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868282, "dur": 71, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868356, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868383, "dur": 32, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868419, "dur": 26, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868448, "dur": 83, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868533, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868567, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868593, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868619, "dur": 88, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868709, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868738, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868766, "dur": 28, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868797, "dur": 23, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868822, "dur": 28, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868854, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868879, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868906, "dur": 28, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868936, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422868963, "dur": 81, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869048, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869076, "dur": 27, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869107, "dur": 26, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869135, "dur": 23, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869160, "dur": 24, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869185, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869186, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869212, "dur": 24, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869238, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869321, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869349, "dur": 31, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869383, "dur": 24, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869409, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869411, "dur": 30, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869444, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869470, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869494, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869522, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869605, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869631, "dur": 155, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869794, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422869845, "dur": 450, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870298, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870355, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870360, "dur": 42, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870407, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870410, "dur": 72, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870486, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870489, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870536, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870539, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870584, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870587, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870624, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870628, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870670, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870673, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870717, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870721, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870746, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870749, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870780, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870783, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870828, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870831, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870873, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870876, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870931, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870973, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422870975, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871016, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871018, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871054, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871056, "dur": 31, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871090, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871092, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871136, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871140, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871181, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871183, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871220, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871223, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871260, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871262, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871295, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871297, "dur": 52, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871352, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871355, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871399, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871402, "dur": 41, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871446, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871451, "dur": 36, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871490, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871492, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871536, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871538, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871584, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871586, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871640, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871643, "dur": 38, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871685, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871688, "dur": 37, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871726, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871729, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871763, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871765, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871805, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871808, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871850, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871853, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871885, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871888, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871922, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871924, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871981, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422871983, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872021, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872023, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872061, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872065, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872106, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872108, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872140, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872142, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872174, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872177, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872214, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872217, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872253, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872255, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872295, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872298, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872335, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872337, "dur": 37, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872378, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872381, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872431, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872433, "dur": 52, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872489, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872493, "dur": 46, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872542, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872545, "dur": 46, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872594, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872597, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872649, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872653, "dur": 38, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872694, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872697, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872736, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872739, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872776, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872779, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872816, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872819, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872858, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872861, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872904, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872907, "dur": 36, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872947, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872951, "dur": 46, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422872999, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873002, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873040, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873043, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873076, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873078, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873108, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873110, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873148, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873150, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873183, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873186, "dur": 33, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873223, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873226, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873260, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873263, "dur": 110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873376, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873380, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873413, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873416, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873452, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873455, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873500, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422873502, "dur": 5916, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879428, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879434, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879466, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879469, "dur": 364, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879836, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879839, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879887, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879890, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422879918, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422880083, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422880114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422880116, "dur": 3508, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883635, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883641, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883721, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883723, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883763, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883765, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883828, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422883866, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884214, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884251, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884254, "dur": 234, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884496, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884498, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884541, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884544, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884592, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884594, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884634, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884636, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884690, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884729, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884731, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884767, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884769, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884810, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884845, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884847, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884956, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884995, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422884998, "dur": 371, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885374, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885412, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885459, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885461, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885490, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885492, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885520, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885557, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885595, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885597, "dur": 118, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885720, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885764, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885766, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885797, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885826, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885829, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885864, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885867, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885888, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885944, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885946, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422885977, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886006, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886008, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886035, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886099, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886130, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886131, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886270, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886300, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886340, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886379, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886380, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886414, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886442, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886470, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886472, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886507, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886509, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886547, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886590, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886591, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886628, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886630, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886675, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886678, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886712, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886713, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886760, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886763, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886795, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886878, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886913, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886948, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886950, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886987, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422886989, "dur": 181, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887175, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887216, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887218, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887250, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887252, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887308, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887345, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887440, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887474, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887476, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887513, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887571, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887608, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887610, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887641, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887752, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887793, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887794, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887826, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887827, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422887857, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888015, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888050, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888053, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888086, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888090, "dur": 262, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888356, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888400, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888402, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888442, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888444, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888507, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888545, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888581, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888583, "dur": 305, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888894, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888932, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422888933, "dur": 220, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889158, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889195, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889197, "dur": 167, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889367, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889369, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889406, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889441, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889444, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889487, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422889512, "dur": 575, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890089, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890139, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890142, "dur": 42, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890188, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890221, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890390, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890418, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890453, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890455, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890506, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890540, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890542, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890574, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890678, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890719, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890721, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890755, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890872, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890908, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890910, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890963, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422890995, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891027, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891063, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891065, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891132, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891169, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891200, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891226, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891444, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891483, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891512, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891513, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891586, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891617, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891620, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891731, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891764, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891941, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422891981, "dur": 199, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422892183, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422892220, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422892330, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422892358, "dur": 694, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893057, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893089, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893091, "dur": 157, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893253, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893280, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893403, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893436, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893437, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893577, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422893604, "dur": 691, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422894300, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422894302, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422894334, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422894338, "dur": 77658, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422972006, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422972010, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422972043, "dur": 3245, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422975293, "dur": 5251, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980555, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980560, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980590, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980592, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980626, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980668, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980670, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980709, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980711, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980746, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980790, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980792, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980819, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980892, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980927, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980974, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422980976, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422981015, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422981018, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422981048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422981050, "dur": 1449, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422982503, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422982534, "dur": 395, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422982932, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422982963, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422982966, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983346, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983381, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983384, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983432, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983434, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983481, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983524, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983556, "dur": 282, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983843, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422983870, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422984070, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422984101, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422984102, "dur": 286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422984391, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422984421, "dur": 828, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422985252, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422985284, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422985367, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422985401, "dur": 693, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986100, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986137, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986139, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986180, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986215, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986245, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986278, "dur": 339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986621, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986656, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986686, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986689, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986800, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986831, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986833, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986901, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422986933, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987289, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987320, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987363, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987495, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987534, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987536, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987570, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987572, "dur": 331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987908, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987938, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987939, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987968, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422987970, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988000, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988030, "dur": 329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988365, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988379, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988419, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988450, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988453, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988485, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988517, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988606, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988637, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988664, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988692, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988721, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988783, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988813, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988818, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422988993, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989026, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989028, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989064, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989066, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989099, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989102, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989135, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989138, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989171, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989173, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989206, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989209, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989243, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989245, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989277, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989313, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989316, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989359, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989363, "dur": 40, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989406, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989409, "dur": 35, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989447, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989450, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989486, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989488, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989536, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989540, "dur": 47, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989591, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989593, "dur": 210, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989807, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989849, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989851, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989881, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989925, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989955, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989956, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422989989, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422990023, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046422990057, "dur": 1234060, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424224127, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424224132, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424224156, "dur": 23, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424224180, "dur": 9577, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424233765, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424233769, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424233822, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424233824, "dur": 15898, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249732, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249736, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249785, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249789, "dur": 124, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249918, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249975, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424249978, "dur": 68510, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424318496, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424318504, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424318554, "dur": 30, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424318585, "dur": 26351, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424344947, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424344952, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424345004, "dur": 4, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424345009, "dur": 1371, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424346385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424346387, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424346434, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424346459, "dur": 932, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424347397, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424347448, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424347449, "dur": 9012, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424356472, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424356477, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424356527, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424356532, "dur": 533, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357071, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357132, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357160, "dur": 631, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357796, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424357835, "dur": 352, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749046424358191, "dur": 7688, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5696, "tid": 41, "ts": 1749046424383972, "dur": 1747, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5696, "tid": 8589934592, "ts": 1749046422808160, "dur": 135692, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749046422943854, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749046422943859, "dur": 1160, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5696, "tid": 41, "ts": 1749046424385721, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5696, "tid": 4294967296, "ts": 1749046422789081, "dur": 1578200, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749046422793196, "dur": 8912, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749046424367373, "dur": 5495, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749046424371106, "dur": 220, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749046424372953, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5696, "tid": 41, "ts": 1749046424385731, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749046422839805, "dur": 1897, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046422841711, "dur": 1383, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046422843237, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749046422843316, "dur": 521, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046422844580, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749046422845731, "dur": 1826, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749046422860408, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1749046422843862, "dur": 25317, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046422869195, "dur": 1487519, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046424356715, "dur": 262, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046424357248, "dur": 66, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046424357342, "dur": 1037, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749046422844001, "dur": 25208, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422869247, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422869343, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422869900, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422869957, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422870063, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422870167, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422870322, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F21D3FF6DC4634B4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422870413, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_CB49B96AA053BB61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422870594, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422870760, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422870856, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749046422871390, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422871441, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422871498, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422871612, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422871728, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422871807, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422871874, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422871955, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422872044, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749046422872248, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422872314, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422872718, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422872830, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749046422872932, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422873647, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422874305, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422874532, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422875268, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422875476, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422876148, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422876993, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422877521, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422877732, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422877931, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422878172, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422878399, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422878633, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422879100, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422879535, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422879804, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422880070, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422880297, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422881012, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422881660, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422882229, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422882554, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422883221, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422884024, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422884205, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422884301, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422884942, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422885031, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422885798, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422886211, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422886295, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422886540, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749046422886734, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422887232, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422887298, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422887828, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422887916, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422888124, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422888537, "dur": 4226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422892763, "dur": 87266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422980030, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422982381, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422982493, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422985635, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422985830, "dur": 2691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749046422988522, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422988628, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422988722, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422989055, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749046422989384, "dur": 1367339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422844101, "dur": 25154, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422869265, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422869737, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EA7E59FB55CB0915.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422869832, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_30E5849A6622DFB0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422870019, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422870109, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_2972E28E15539B90.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422870277, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_50E1A15E13819776.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422870344, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422870491, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422870724, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4D1C445C9CC0E084.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422870817, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4D1C445C9CC0E084.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422871047, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422871170, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422871241, "dur": 8060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422879302, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422879430, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422879635, "dur": 3440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422883260, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422883379, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422883863, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422884032, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422884154, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422884847, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422885010, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749046422885211, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422885271, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422885912, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422885975, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422886058, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422886693, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422887443, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422888095, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422888522, "dur": 4156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422892678, "dur": 55489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422948167, "dur": 29381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422977552, "dur": 2899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422980452, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422980544, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422982916, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422983079, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422985582, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422985672, "dur": 3150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422988823, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422988905, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749046422989074, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749046422989582, "dur": 1367161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422844149, "dur": 25137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422869298, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749046422869926, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B4A1BF472DE35F91.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749046422870008, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422870073, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7FFEF131F3AD3BA4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749046422870321, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749046422870470, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422870958, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749046422871304, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422871732, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749046422871840, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749046422871917, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422872034, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749046422872094, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422872148, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749046422872220, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749046422872382, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422872524, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749046422872839, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422873004, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422873753, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422874324, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422874521, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422875318, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422875600, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422876529, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422876729, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422877284, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422877935, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422878203, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422878432, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422878671, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422878876, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422879167, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422879437, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422879672, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422880144, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422880883, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422881233, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422882562, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422883232, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422883856, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749046422884047, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422884746, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422885047, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422886046, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422886187, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422886551, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749046422886699, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422886776, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422887228, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422887319, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422887456, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422888117, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422888520, "dur": 4159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422892680, "dur": 84865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422977547, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422979981, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422980351, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422982950, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422983034, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422985660, "dur": 785, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422986456, "dur": 2970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749046422989427, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749046422989508, "dur": 1367246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422844131, "dur": 25138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422869280, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422869895, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B4FC9B590A58509F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422869967, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422870055, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FA7F2E82B1549095.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422870181, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422870254, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_88FDBEF459069934.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422870572, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422870948, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422871048, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749046422871272, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422871416, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749046422871661, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422871767, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749046422872052, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749046422872801, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749046422872908, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422873470, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422874139, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422874404, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422874964, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422875492, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422876158, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\GraphDataUtils.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749046422876050, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422876953, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422877944, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422878178, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422878389, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422878736, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422879131, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422879457, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422879653, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422880123, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422880664, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422881392, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422882129, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422882344, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422882494, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422883188, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422883847, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422884010, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422884086, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422884255, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749046422884882, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422885368, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422885508, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422886058, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422886671, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422887431, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422887571, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749046422887896, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422887981, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422888123, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422888523, "dur": 4151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422892676, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749046422892952, "dur": 84608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422977562, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749046422980404, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422980548, "dur": 2899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749046422983448, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422983956, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749046422986698, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422987098, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422987568, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422988066, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422988282, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422988342, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422988698, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749046422988986, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749046422989092, "dur": 1367615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422844035, "dur": 25195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422869248, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422869350, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422870026, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422870131, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422870360, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_6241537ACFF4D8B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422870655, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0F4B26DD7F6C67C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422871054, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749046422871279, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422871706, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422871892, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422872243, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422872307, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422872450, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749046422872633, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422872792, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749046422872918, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422873526, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422874307, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422874509, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422875300, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422875563, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422876053, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422877592, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@d680fce5716f\\Runtime\\RendererFeatures\\ScreenSpaceShadows.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749046422876787, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422878202, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422878409, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422879052, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422879777, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422880049, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422880274, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422880892, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422881087, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422881911, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422882061, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422882605, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422883216, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422883865, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422884092, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422884339, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749046422885194, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422885374, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422885572, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422885652, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422886053, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422886676, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422887429, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749046422887613, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749046422888118, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1749046422888621, "dur": 133, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422889060, "dur": 82483, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1749046422977558, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749046422979959, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422980491, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749046422982805, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422982950, "dur": 2516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749046422985467, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422986184, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749046422988622, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422988903, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422989068, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749046422989425, "dur": 1367304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422844077, "dur": 25165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422869251, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422869511, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422869702, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_54FADE420F1D3CF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422869771, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422869850, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422870346, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422870625, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422870858, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422871085, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749046422871258, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422871405, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422871693, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422871795, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422871850, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749046422872151, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749046422872759, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749046422872925, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422873686, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422874276, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422874652, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422875338, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422875821, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422876727, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422877368, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422878587, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422878802, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422879026, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422879771, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422880018, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422880275, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422880828, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422881099, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422881746, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422882295, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422882697, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422883217, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422883873, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422884063, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422884251, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422884315, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422884841, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422885125, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422885391, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422885961, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422886081, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422886536, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422886703, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422886860, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422887240, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422887331, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422887467, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422888122, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422888527, "dur": 4145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422892674, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749046422892892, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422892959, "dur": 84623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422977583, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422980029, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422980219, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422982805, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422982933, "dur": 2619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422985553, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422985675, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749046422988531, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046422988810, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749046422988908, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749046422989082, "dur": 1355240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749046424344340, "dur": 2567, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749046424344323, "dur": 2586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749046424346937, "dur": 9780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422844195, "dur": 25140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422869348, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749046422870022, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422870317, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422870558, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749046422870710, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422870824, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_CAECCED5B87D25B6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749046422870951, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422871070, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749046422871505, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749046422871648, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422872093, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422872360, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422872720, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749046422872822, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422873214, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422873910, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422874450, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422874850, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422875560, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422876497, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422877277, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422877851, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422878950, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422879602, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422879943, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422880342, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422880837, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422881462, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422882121, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422882373, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422882487, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422883216, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422883874, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749046422884158, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422884882, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422885138, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422885330, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749046422885578, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422885662, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422886922, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422887040, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422887448, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422888096, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422888507, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749046422888625, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422888925, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422889469, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422889593, "dur": 3152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422892745, "dur": 84823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422977570, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422980009, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422980174, "dur": 3152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422983327, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422983401, "dur": 2593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422985996, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749046422986361, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749046422989405, "dur": 1367322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422844184, "dur": 25122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422869319, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422869876, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422869953, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422870174, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422870226, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422870329, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422870396, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422870696, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_D77C7CCB1D31CDA5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422871077, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749046422871254, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422871328, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422871422, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749046422871636, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422871971, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1749046422872594, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422872841, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422873002, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422873841, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422874309, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422874508, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422875094, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422875319, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422875615, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422876475, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422877021, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422877595, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422877803, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422878278, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422878491, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422878740, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422878987, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422879480, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422879761, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422880045, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422880245, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422880675, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422881249, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422881921, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422882193, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422882493, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422883206, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422883871, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422884038, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422884091, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422884305, "dur": 1457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422885763, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422885862, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422886111, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422886633, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422886771, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422887459, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422888094, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422888505, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749046422888725, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422889149, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422889258, "dur": 3496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422892754, "dur": 84784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422977540, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422979934, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422980069, "dur": 2771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422982842, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422982944, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422985463, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422985772, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749046422988768, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046422988907, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749046422989081, "dur": 1238884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046424227989, "dur": 18064, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749046424227967, "dur": 19611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749046424249228, "dur": 216, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749046424249842, "dur": 68193, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749046424344323, "dur": 11621, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749046424344313, "dur": 11633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749046424355973, "dur": 659, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749046422844229, "dur": 25127, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422869369, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422870000, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422870238, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E120A74A668A048A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422870355, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422870883, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422871151, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749046422871207, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422871338, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749046422871511, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749046422871776, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749046422871923, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422871973, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749046422872087, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422872192, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749046422872361, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422872833, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749046422872895, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422873376, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422874231, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422874433, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422875003, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422875330, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422875688, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422876577, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422876782, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422877342, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422877762, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422878294, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422878502, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422878723, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422879074, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422879809, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422880342, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422880983, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422881370, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422882137, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422882499, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422883202, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422883863, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422884152, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422884884, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422885007, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422885075, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422885473, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422885548, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422886133, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422886279, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422886512, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422887048, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422887179, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422887453, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422888120, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422888517, "dur": 1781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422890300, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749046422890435, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422890510, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422890934, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422891051, "dur": 1656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422892707, "dur": 84834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422977542, "dur": 2561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422980104, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422980211, "dur": 4518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422984730, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422984824, "dur": 3679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749046422988504, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422988663, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046422989088, "dur": 1355238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749046424344343, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749046424344327, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749046424344506, "dur": 1428, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749046424345937, "dur": 10836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422844262, "dur": 25116, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422869389, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422870023, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422870418, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_36273396CDE3C103.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422870817, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_672767AC8B5C81E9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422871257, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422871500, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749046422871841, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749046422871973, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749046422872739, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749046422872853, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422873018, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422873628, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422874236, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422874439, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422875001, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422875377, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422875983, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422877008, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422877505, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422877714, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422877927, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422878194, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422878415, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422878647, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422878854, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422879084, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422879805, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422880261, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422880873, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422881354, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422882075, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422882298, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422882492, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422883207, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422883843, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422883991, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422884100, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422884370, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422885228, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422885581, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422885770, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422886119, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422886795, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422887453, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422887632, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422888375, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422888515, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422888713, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422889221, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422889330, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422889440, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422890002, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422890105, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749046422890271, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422890625, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422890728, "dur": 1989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422892717, "dur": 84834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422977563, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422980209, "dur": 1838, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422982062, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422984837, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422984931, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749046422987529, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422987979, "dur": 688, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422988835, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422989071, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749046422989515, "dur": 1367241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422844306, "dur": 25090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422869411, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422870096, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5E49D6BEFCDFF46D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422870175, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422870320, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422870483, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422870895, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749046422871160, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422871366, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422871490, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422871590, "dur": 7253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422878845, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422879022, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422880184, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422880924, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422881111, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422881575, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422882555, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422883219, "dur": 859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422884080, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422884348, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422884507, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422885231, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422885445, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422885606, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422885994, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422886596, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422886767, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422887446, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422888114, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422888503, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422888674, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422888993, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422889426, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422889554, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422889744, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422890428, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422890616, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749046422890782, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422891200, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422891314, "dur": 1368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422892683, "dur": 84860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422977554, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422980100, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422980191, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422982585, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422983075, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422985648, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422986229, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749046422989318, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749046422989416, "dur": 1367332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422844333, "dur": 25082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422869426, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422870038, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422870349, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422870520, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422870652, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F4B95EF39F1A8010.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422870737, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422870887, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749046422870942, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422870995, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749046422871156, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422871206, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749046422871351, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749046422871629, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422872030, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749046422872527, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749046422872879, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422873327, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422874056, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422874346, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422874546, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422875136, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422875343, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422876044, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@0e6324a629e1\\Editor\\Data\\Graphs\\VirtualTextureInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749046422875931, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422877143, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422877631, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422878079, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422878276, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422878488, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422878727, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422879064, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422879578, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422879799, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422880074, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422880833, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422881528, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422882210, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422882515, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422883209, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422884077, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422885012, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422885410, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422886189, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422886304, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422886427, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422886665, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422887436, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422887574, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422887932, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422888098, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422888502, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749046422888722, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422889231, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422889356, "dur": 3380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422892736, "dur": 84818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422977556, "dur": 2883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422980441, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422980582, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422982931, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422983039, "dur": 2575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422985615, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422985677, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749046422988498, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422988625, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749046422988814, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749046422989002, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749046422989107, "dur": 1367598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422844366, "dur": 25065, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422869441, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422870324, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_B0964A0CA2EEEE2D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422870377, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_42313A2B468B0196.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422870544, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422870662, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422871062, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749046422871242, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749046422871382, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749046422871516, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749046422872036, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749046422872300, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1749046422872449, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749046422872819, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422873525, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422874087, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422874321, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422874514, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422875106, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422875340, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422875794, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422876634, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422876857, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422877280, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422877948, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422878177, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422878397, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422879021, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422880016, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422880284, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422880917, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422881224, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422881845, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422882121, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422882485, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422883190, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422883845, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422883985, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422884046, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422886418, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422886535, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422886727, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422887868, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422887972, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422888110, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422889108, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422889230, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749046422889404, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422889869, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422889984, "dur": 2742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422892727, "dur": 84849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422977579, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422979981, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422980531, "dur": 2977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422983509, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422983961, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422986703, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749046422986845, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749046422989565, "dur": 1367175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422844397, "dur": 25050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422869457, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422869665, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1FDEA3861C839C9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422869856, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_326BFA302771D2EF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422869918, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422870048, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_233FB08ADCB5306F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422870132, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_233FB08ADCB5306F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422870371, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422870671, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E79D22299C4825D4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422870763, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749046422871009, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422871078, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422871275, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422871370, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422871459, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422871514, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749046422871735, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749046422872085, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749046422872249, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749046422872369, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422872433, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422872526, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749046422872856, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422873343, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422874223, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422874437, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422875150, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422875372, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422875839, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422876586, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422876829, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422877450, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422877698, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422877910, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422878539, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422878756, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422878987, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422879744, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422879999, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422880643, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422881186, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422881714, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422882242, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422882754, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422883198, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422883850, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422884073, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422884827, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422884936, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422885129, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422885355, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422885523, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422886132, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422886515, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749046422886677, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422886736, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422887152, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422887337, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422887457, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422888118, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422888526, "dur": 4150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422892677, "dur": 51887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422946000, "dur": 525, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 14, "ts": 1749046422946526, "dur": 1555, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 14, "ts": 1749046422948081, "dur": 74, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 14, "ts": 1749046422944566, "dur": 3595, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422948161, "dur": 29402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422977576, "dur": 2514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422980091, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422980270, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422982800, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422982948, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422985597, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422985706, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749046422988481, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422988582, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422988703, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749046422988948, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749046422989059, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749046422989413, "dur": 1367317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422844428, "dur": 25033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422869514, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422869688, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422869782, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422869933, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422870037, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BEBB047E8EA0A5FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422870160, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_94383F50AE2F3B27.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422870243, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422870300, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_DA6971D49F67486A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422870486, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422870663, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422870722, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422870815, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422871278, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422871640, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422871902, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1749046422872184, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749046422872367, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422872567, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749046422872825, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422873231, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422873832, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422874150, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422874364, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422874739, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422875369, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422876028, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422876795, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422877318, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422877747, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422877956, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422878186, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422878404, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422878641, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422878855, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422879104, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422879717, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422879994, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422880592, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422881082, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422881806, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422882152, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422882486, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422883201, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422883867, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422884036, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422884130, "dur": 852, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422884983, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046422885839, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422886023, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422886328, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046422886919, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422887030, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422887461, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422888126, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422888513, "dur": 1599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422890113, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422890296, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046422891681, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422891765, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422891887, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046422892533, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046422892677, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749046422892808, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046422893135, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046422894510, "dur": 1329165, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046424228329, "dur": 4787, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749046424227958, "dur": 5229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749046424233189, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046424233284, "dur": 12766, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749046424233281, "dur": 14292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749046424248877, "dur": 175, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749046424249656, "dur": 68365, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749046424344330, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749046424344321, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749046424344449, "dur": 12293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422844464, "dur": 25010, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422869476, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422869698, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_13ED6B36C70FAB0A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422869766, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422869844, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4D5EF4ECE256D09B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422870021, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422870077, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0C1C2D14907EB90E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422870364, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422870531, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422870866, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422871257, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422871312, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749046422871445, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749046422871652, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422871704, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749046422872016, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749046422872249, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749046422872375, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422872703, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749046422872819, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422873324, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422873942, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422874260, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422874452, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422875095, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422875348, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422875815, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422877040, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422877683, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422877930, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422878190, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422878433, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422878728, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422878930, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422879294, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422879586, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422879800, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422880069, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422880281, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422881020, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422881526, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422882236, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422882735, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422883212, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422884044, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422884278, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422884868, "dur": 1052, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422885935, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422886063, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422886541, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422886682, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422886784, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422887248, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422887343, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422887439, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422887978, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422888097, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422889134, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422889255, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422889438, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422890162, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422890291, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422890433, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422890918, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422891018, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749046422891143, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422891521, "dur": 1176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422892697, "dur": 84860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422977560, "dur": 2663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422980224, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422980454, "dur": 2902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422983362, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422983633, "dur": 2987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422986620, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422986880, "dur": 2414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749046422989295, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749046422989404, "dur": 1367330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749046424363215, "dur": 1767, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5696, "tid": 41, "ts": 1749046424386243, "dur": 3149, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5696, "tid": 41, "ts": 1749046424389426, "dur": 1490, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5696, "tid": 41, "ts": 1749046424381602, "dur": 10853, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}