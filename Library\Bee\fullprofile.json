{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5696, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5696, "tid": 44, "ts": 1749048181793886, "dur": 920, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749048181798787, "dur": 676, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5696, "tid": 1, "ts": 1749048181388045, "dur": 5245, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749048181393294, "dur": 65594, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5696, "tid": 1, "ts": 1749048181458899, "dur": 40727, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749048181799466, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 5696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181386021, "dur": 7787, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181393811, "dur": 388216, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181394854, "dur": 2517, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181397379, "dur": 1730, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399114, "dur": 322, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399437, "dur": 11, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399449, "dur": 39, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399493, "dur": 31, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399528, "dur": 47, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399579, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399581, "dur": 36, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399621, "dur": 67, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399695, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399743, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399745, "dur": 30, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399778, "dur": 56, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399838, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399872, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399873, "dur": 34, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399910, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399912, "dur": 30, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399947, "dur": 29, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181399979, "dur": 28, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400010, "dur": 32, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400044, "dur": 32, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400078, "dur": 29, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400109, "dur": 25, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400137, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400160, "dur": 27, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400189, "dur": 28, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400220, "dur": 27, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400249, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400251, "dur": 27, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400280, "dur": 24, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400306, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400339, "dur": 23, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400364, "dur": 26, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400393, "dur": 30, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400426, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400462, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400463, "dur": 35, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400500, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400502, "dur": 31, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400535, "dur": 27, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400564, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400588, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400614, "dur": 29, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400645, "dur": 22, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400670, "dur": 33, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400707, "dur": 27, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400736, "dur": 25, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400763, "dur": 31, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400796, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400823, "dur": 30, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400856, "dur": 23, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400881, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400883, "dur": 31, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400915, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400917, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400944, "dur": 30, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400975, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181400976, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401005, "dur": 26, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401033, "dur": 25, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401060, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401090, "dur": 14, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401107, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401133, "dur": 26, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401162, "dur": 25, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401189, "dur": 25, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401217, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401218, "dur": 34, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401256, "dur": 25, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401284, "dur": 32, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401319, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401321, "dur": 28, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401352, "dur": 26, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401381, "dur": 28, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401411, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401412, "dur": 28, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401443, "dur": 27, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401473, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401498, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401524, "dur": 61, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401589, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401591, "dur": 26, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401620, "dur": 27, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401651, "dur": 75, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401729, "dur": 23, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401756, "dur": 24, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401783, "dur": 26, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401810, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401812, "dur": 30, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401844, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401871, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401901, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401925, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401953, "dur": 28, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181401984, "dur": 27, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402013, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402040, "dur": 22, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402064, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402091, "dur": 24, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402116, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402118, "dur": 27, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402146, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402148, "dur": 27, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402176, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402177, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402204, "dur": 27, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402232, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402234, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402262, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402263, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402287, "dur": 25, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402315, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402339, "dur": 25, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402366, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402392, "dur": 30, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402423, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402425, "dur": 26, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402453, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402480, "dur": 23, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402506, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402528, "dur": 141, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402673, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402707, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402709, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402742, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402743, "dur": 31, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402777, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402778, "dur": 31, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402812, "dur": 26, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402841, "dur": 26, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402870, "dur": 23, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402895, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402923, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402952, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181402977, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403005, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403034, "dur": 26, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403063, "dur": 22, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403087, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403113, "dur": 3, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403117, "dur": 27, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403146, "dur": 27, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403175, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403177, "dur": 27, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403206, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403207, "dur": 26, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403236, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403263, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403264, "dur": 19, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403285, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403312, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403339, "dur": 26, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403367, "dur": 25, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403396, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403423, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403451, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403480, "dur": 26, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403508, "dur": 22, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403533, "dur": 23, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403558, "dur": 27, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403587, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403589, "dur": 40, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403632, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403634, "dur": 43, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403680, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403681, "dur": 36, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403720, "dur": 31, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403753, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403755, "dur": 28, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403786, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403817, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403844, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403845, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403873, "dur": 28, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403904, "dur": 25, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403931, "dur": 33, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403965, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181403967, "dur": 113, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404083, "dur": 42, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404127, "dur": 1, "ph": "X", "name": "ProcessMessages 1914", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404129, "dur": 21, "ph": "X", "name": "ReadAsync 1914", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404152, "dur": 26, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404182, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404208, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404209, "dur": 30, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404240, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404242, "dur": 28, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404273, "dur": 26, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404301, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404326, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404355, "dur": 36, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404393, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404418, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404444, "dur": 25, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404471, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404472, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404498, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404532, "dur": 27, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404560, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404561, "dur": 27, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404591, "dur": 24, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404617, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404643, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404665, "dur": 33, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404700, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404702, "dur": 28, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404733, "dur": 32, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404766, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404767, "dur": 27, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404797, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404822, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404846, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404870, "dur": 29, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404901, "dur": 26, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404930, "dur": 29, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404962, "dur": 24, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181404988, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405016, "dur": 23, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405041, "dur": 29, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405072, "dur": 23, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405097, "dur": 27, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405126, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405151, "dur": 29, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405182, "dur": 27, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405210, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405212, "dur": 31, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405244, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405246, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405277, "dur": 23, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405303, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405326, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405352, "dur": 25, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405379, "dur": 31, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405411, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405413, "dur": 28, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405443, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405472, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405497, "dur": 28, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405527, "dur": 23, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405552, "dur": 25, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405579, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405608, "dur": 25, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405636, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405663, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405702, "dur": 24, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405726, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405728, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405756, "dur": 22, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405780, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405802, "dur": 31, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405836, "dur": 29, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405867, "dur": 27, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405897, "dur": 28, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405928, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405950, "dur": 41, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181405995, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406023, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406025, "dur": 31, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406059, "dur": 24, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406085, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406087, "dur": 30, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406119, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406120, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406147, "dur": 26, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406176, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406205, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406207, "dur": 23, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406232, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406255, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406279, "dur": 25, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406308, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406334, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406365, "dur": 31, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406398, "dur": 24, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406424, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406451, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406477, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406480, "dur": 28, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406510, "dur": 28, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406541, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406570, "dur": 24, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406596, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406621, "dur": 29, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406653, "dur": 29, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406683, "dur": 24, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406710, "dur": 24, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406736, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406763, "dur": 24, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406790, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406817, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406819, "dur": 25, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181406957, "dur": 46, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407004, "dur": 2, "ph": "X", "name": "ProcessMessages 3336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407007, "dur": 24, "ph": "X", "name": "ReadAsync 3336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407033, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407059, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407089, "dur": 26, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407118, "dur": 27, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407148, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407174, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407199, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407226, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407258, "dur": 27, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407287, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407289, "dur": 29, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407322, "dur": 26, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407351, "dur": 27, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407379, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407381, "dur": 41, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407424, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407460, "dur": 24, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407487, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407507, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407535, "dur": 27, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407564, "dur": 30, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407596, "dur": 26, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407624, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407652, "dur": 25, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407679, "dur": 28, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407709, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407735, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407759, "dur": 24, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407785, "dur": 24, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407812, "dur": 29, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407843, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407874, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407876, "dur": 33, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407913, "dur": 36, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407952, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407955, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181407991, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408020, "dur": 22, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408045, "dur": 27, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408075, "dur": 26, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408104, "dur": 24, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408131, "dur": 31, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408163, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408165, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408191, "dur": 25, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408218, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408244, "dur": 22, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408268, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408297, "dur": 26, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408325, "dur": 31, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408359, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408390, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408419, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408444, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408469, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408471, "dur": 23, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408496, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408525, "dur": 25, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408553, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408579, "dur": 26, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408607, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408635, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408659, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408686, "dur": 46, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408735, "dur": 28, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408766, "dur": 24, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408792, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408794, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408844, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408874, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408901, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181408902, "dur": 96, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409000, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409028, "dur": 23, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409054, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409081, "dur": 25, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409108, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409138, "dur": 26, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409166, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409167, "dur": 25, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409193, "dur": 30, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409226, "dur": 59, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409288, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409334, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409335, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409370, "dur": 26, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409399, "dur": 64, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409465, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409496, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409523, "dur": 26, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409551, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409621, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409650, "dur": 25, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409678, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409711, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409712, "dur": 75, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409790, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409820, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409823, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409849, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409851, "dur": 141, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181409995, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410033, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410035, "dur": 45, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410082, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410119, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410121, "dur": 66, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410190, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410192, "dur": 27, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410221, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410223, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410309, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410354, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410355, "dur": 47, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410405, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410406, "dur": 31, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410439, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410441, "dur": 67, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410512, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410556, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410560, "dur": 34, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410596, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410598, "dur": 87, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410687, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410719, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410721, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410757, "dur": 33, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410791, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410793, "dur": 88, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410885, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410918, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410920, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410952, "dur": 22, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181410977, "dur": 92, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411072, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411099, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411102, "dur": 27, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411131, "dur": 28, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411161, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411163, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411250, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411297, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411299, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411328, "dur": 30, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411360, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411436, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411470, "dur": 29, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411501, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411504, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411527, "dur": 90, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411621, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411650, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411651, "dur": 27, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411681, "dur": 25, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411710, "dur": 235, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411949, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411990, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181411992, "dur": 33, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412029, "dur": 36, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412068, "dur": 43, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412114, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412116, "dur": 36, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412155, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412157, "dur": 77, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412238, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412275, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412277, "dur": 42, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412321, "dur": 32, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412355, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412357, "dur": 33, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412392, "dur": 26, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412421, "dur": 22, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412445, "dur": 30, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412477, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412480, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412511, "dur": 72, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412585, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412614, "dur": 28, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412646, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412673, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412675, "dur": 81, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412759, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412795, "dur": 64, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412862, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412864, "dur": 43, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412909, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412910, "dur": 30, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412942, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181412944, "dur": 85, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413031, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413068, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413070, "dur": 38, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413110, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413112, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413140, "dur": 23, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413166, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413192, "dur": 33, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413228, "dur": 26, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413257, "dur": 35, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413294, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413297, "dur": 88, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413389, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413431, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413433, "dur": 29, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413465, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413489, "dur": 89, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413581, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413614, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413615, "dur": 33, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413650, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413652, "dur": 25, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413680, "dur": 31, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413714, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413715, "dur": 25, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413743, "dur": 31, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413776, "dur": 23, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413802, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413821, "dur": 25, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413849, "dur": 30, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413883, "dur": 64, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413950, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181413981, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414010, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414038, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414040, "dur": 80, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414124, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414160, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414161, "dur": 32, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414195, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414198, "dur": 29, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414228, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414230, "dur": 25, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414256, "dur": 25, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414285, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414314, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414339, "dur": 26, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414366, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414449, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414489, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414490, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414525, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414527, "dur": 72, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414602, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414635, "dur": 2, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414639, "dur": 29, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414670, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414671, "dur": 24, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414698, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414776, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414806, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414808, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414838, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414839, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414872, "dur": 24, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414898, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414928, "dur": 25, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414956, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181414981, "dur": 24, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415008, "dur": 60, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415070, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415098, "dur": 25, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415126, "dur": 23, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415151, "dur": 69, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415222, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415252, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415281, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415303, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415378, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415404, "dur": 32, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415438, "dur": 31, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415471, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415496, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415497, "dur": 31, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415530, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415531, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415558, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415560, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415585, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415610, "dur": 22, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415634, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415704, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415736, "dur": 24, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415763, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415790, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415868, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415897, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415921, "dur": 34, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415959, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415990, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181415991, "dur": 40, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416035, "dur": 82, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416119, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416150, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416176, "dur": 22, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416200, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416223, "dur": 77, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416303, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416334, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416336, "dur": 30, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416368, "dur": 25, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416396, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416397, "dur": 68, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416467, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416494, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416519, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416521, "dur": 29, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416552, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416553, "dur": 37, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416592, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416625, "dur": 98, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416727, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416762, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416763, "dur": 29, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416795, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416797, "dur": 29, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416829, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416831, "dur": 80, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416914, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416950, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416952, "dur": 26, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416979, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181416981, "dur": 28, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417012, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417093, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417125, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417127, "dur": 27, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417157, "dur": 83, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417243, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417272, "dur": 28, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417302, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417303, "dur": 27, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417332, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417404, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417435, "dur": 28, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417464, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417466, "dur": 78, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417547, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417584, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417586, "dur": 34, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417622, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417647, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417649, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417736, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417774, "dur": 22, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417798, "dur": 38, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417838, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417839, "dur": 81, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417924, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417959, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417960, "dur": 27, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181417989, "dur": 31, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418023, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418025, "dur": 29, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418056, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418059, "dur": 78, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418140, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418171, "dur": 30, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418205, "dur": 84, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418291, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418325, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418326, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418353, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418381, "dur": 22, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418406, "dur": 86, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418495, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418524, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418526, "dur": 36, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418564, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418566, "dur": 75, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418644, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418676, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418678, "dur": 27, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418715, "dur": 32, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418749, "dur": 63, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418815, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418841, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418868, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418894, "dur": 80, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181418977, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419006, "dur": 31, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419039, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419066, "dur": 77, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419147, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419181, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419207, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419209, "dur": 46, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419260, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419262, "dur": 86, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419352, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419388, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419390, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419418, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419446, "dur": 85, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419533, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419535, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419568, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419596, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419621, "dur": 86, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419709, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419741, "dur": 28, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419772, "dur": 24, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419800, "dur": 24, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419826, "dur": 23, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419853, "dur": 24, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419879, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419907, "dur": 23, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181419932, "dur": 81, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420015, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420044, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420072, "dur": 25, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420100, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420126, "dur": 25, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420153, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420181, "dur": 23, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420207, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420286, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420315, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420316, "dur": 39, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420357, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420359, "dur": 30, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420392, "dur": 24, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420420, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420448, "dur": 26, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420476, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420478, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420505, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420585, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420614, "dur": 152, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420769, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420771, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181420815, "dur": 253, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421071, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421113, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421115, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421144, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421180, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421182, "dur": 52, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421238, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421241, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421302, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421305, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421356, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421359, "dur": 36, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421398, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421401, "dur": 48, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421452, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421455, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421496, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421499, "dur": 34, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421536, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421539, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421575, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421578, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421623, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421626, "dur": 47, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421675, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421677, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421713, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421715, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421755, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421757, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421798, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421801, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421846, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421849, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421910, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421913, "dur": 46, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421963, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181421966, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422003, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422006, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422050, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422054, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422104, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422107, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422154, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422158, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422205, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422208, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422256, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422260, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422308, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422311, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422350, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422353, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422388, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422391, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422431, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422434, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422467, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422470, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422500, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422504, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422542, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422544, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422580, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422583, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422621, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422624, "dur": 39, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422666, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422669, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422700, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422703, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422740, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422743, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422790, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422794, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422834, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422840, "dur": 37, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422880, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422883, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422924, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422927, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422976, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181422980, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423020, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423023, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423066, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423069, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423115, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423118, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423162, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423165, "dur": 37, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423207, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423210, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423248, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423251, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423293, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423296, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423337, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423340, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423384, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423386, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423425, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423427, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423468, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423470, "dur": 38, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423511, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423513, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423552, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423554, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423594, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423596, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423640, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423642, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423684, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423687, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423726, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423728, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423763, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423766, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423804, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423807, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423844, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423847, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423878, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423881, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423907, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423909, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423944, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423951, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423991, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181423993, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424031, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424033, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424068, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424071, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424112, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424114, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424158, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424283, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424285, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424332, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424334, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424376, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424379, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424503, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181424540, "dur": 5912, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181430463, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181430468, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181430538, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181430541, "dur": 581, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431128, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431167, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431169, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431203, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431239, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431241, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431413, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431454, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181431457, "dur": 2844, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434307, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434312, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434364, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434366, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434402, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434499, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434527, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434861, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434890, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181434933, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435170, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435209, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435212, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435243, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435271, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435302, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435378, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435409, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435529, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435569, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435571, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435609, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435611, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435647, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435676, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435855, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435883, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435936, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435938, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181435963, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436034, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436069, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436071, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436111, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436164, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436195, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436229, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436258, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436298, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436300, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436334, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436335, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436386, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436426, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436455, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436483, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436485, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436515, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436586, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436618, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436620, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436646, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436648, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436685, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436687, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436718, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436722, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436816, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436818, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436863, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436897, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436899, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436938, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436942, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436969, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436997, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181436999, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437034, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437036, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437066, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437096, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437151, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437183, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437186, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437216, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437218, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437244, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437274, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437276, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437312, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437337, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437366, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437403, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437406, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437440, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437474, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437516, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437552, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437643, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437686, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437726, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437759, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437789, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437791, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437915, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437964, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437966, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181437999, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438001, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438029, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438032, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438066, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438096, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438144, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438170, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438197, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438228, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438230, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438263, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438265, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438301, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438343, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438374, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438376, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438424, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438456, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438458, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438598, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438634, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438745, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438786, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438859, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438861, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438900, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438903, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438937, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181438939, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439003, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439042, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439045, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439082, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439128, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439166, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439198, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439354, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439391, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439429, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439561, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439604, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439608, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439650, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439682, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439684, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439822, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181439861, "dur": 802, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181440667, "dur": 61, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181440733, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181440736, "dur": 378, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441123, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441178, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441180, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441222, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441262, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441264, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441301, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441343, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441345, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441385, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441420, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441452, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441487, "dur": 416, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441909, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441945, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181441948, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442041, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442074, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442111, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442139, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442375, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442414, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442556, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442586, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442587, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442676, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442702, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442818, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442850, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442898, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442935, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181442936, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443171, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443200, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443230, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443372, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443401, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443434, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443460, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443560, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443584, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443859, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443885, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181443918, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181444081, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181444109, "dur": 1009, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181445122, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181445157, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181445159, "dur": 67531, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181512698, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181512701, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181512729, "dur": 1664, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181514398, "dur": 7252, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521659, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521663, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521734, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521737, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521782, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521785, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521821, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521823, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521983, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181521986, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181522020, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181522022, "dur": 205, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181522231, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181522265, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181522267, "dur": 1014, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181523283, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181523287, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181523359, "dur": 564, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181523926, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181523952, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181523983, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524102, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524131, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524133, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524162, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524163, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524241, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524276, "dur": 214, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524495, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524536, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524538, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524575, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524664, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181524694, "dur": 914, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181525612, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181525641, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181525643, "dur": 978, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526625, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526660, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526661, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526712, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526746, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526748, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526788, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526790, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526824, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526859, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526861, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526957, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181526983, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527130, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527160, "dur": 655, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527840, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527843, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527866, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527924, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181527955, "dur": 1026, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181528985, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529017, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529019, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529048, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529049, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529078, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529080, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529105, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529106, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529136, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529138, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529166, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529168, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529197, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529199, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529230, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529232, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529266, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529269, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529296, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529298, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529327, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529357, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529359, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529393, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529395, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529431, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529433, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529475, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529479, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529516, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529518, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529559, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529561, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529598, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529600, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529645, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529648, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529688, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529690, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529723, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529726, "dur": 61, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529790, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529793, "dur": 43, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529839, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529849, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181529885, "dur": 277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530167, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530210, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530212, "dur": 148, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530365, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530406, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530408, "dur": 348, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530760, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530798, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530800, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181530829, "dur": 83834, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181614672, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181614677, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181614703, "dur": 21, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181614725, "dur": 14842, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181629576, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181629581, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181629631, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181629633, "dur": 18019, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647661, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647666, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647723, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647727, "dur": 97, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647829, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647831, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647908, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181647911, "dur": 79538, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181727457, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181727462, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181727509, "dur": 22, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181727533, "dur": 15546, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181743088, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181743092, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181743125, "dur": 23, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181743149, "dur": 8846, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181752005, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181752010, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181752056, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181752060, "dur": 1415, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181753479, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181753481, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181753529, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181753551, "dur": 7715, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761279, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761285, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761323, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761330, "dur": 611, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761948, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181761996, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181762016, "dur": 2227, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181764248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181764251, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181764289, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181764291, "dur": 5331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181769626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181769629, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181769686, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181769688, "dur": 679, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181770372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181770374, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181770427, "dur": 445, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5696, "tid": 12884901888, "ts": 1749048181770876, "dur": 10926, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749048181799479, "dur": 1698, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5696, "tid": 8589934592, "ts": 1749048181383121, "dur": 116547, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749048181499670, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5696, "tid": 8589934592, "ts": 1749048181499678, "dur": 1131, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749048181801180, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5696, "tid": 4294967296, "ts": 1749048181362808, "dur": 420071, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749048181366427, "dur": 8631, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749048181782934, "dur": 5701, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749048181786616, "dur": 324, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5696, "tid": 4294967296, "ts": 1749048181788727, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749048181801187, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749048181392331, "dur": 1508, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181393851, "dur": 932, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181394904, "dur": 75, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749048181394979, "dur": 489, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181396670, "dur": 1056, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_AFA8334515511D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749048181398872, "dur": 1746, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749048181401044, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749048181402853, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749048181413444, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749048181413950, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1749048181395499, "dur": 26280, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181421794, "dur": 349065, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181770860, "dur": 309, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181771228, "dur": 50, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181771422, "dur": 61, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181771528, "dur": 1342, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749048181395657, "dur": 26151, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181421844, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181421962, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_884F51AC56C27FE9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181422468, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181422521, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181422598, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_EFCFBB64C82BDA36.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181422667, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_2972E28E15539B90.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181422846, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_6241537ACFF4D8B0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181423093, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64D3851878536F69.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181423205, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_98C4362969A4A1A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181423425, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749048181423564, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181423671, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749048181423802, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749048181424026, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749048181424201, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749048181424572, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181424800, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749048181424987, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181425266, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181425995, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181426805, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181427042, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181427315, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181427945, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181428214, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181428432, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181429147, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181429371, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181429842, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181430117, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181430329, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181430799, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181431010, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181431493, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181431692, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181431936, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181432145, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181432530, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181433021, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181433594, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181434209, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181434483, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181435018, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181435505, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181436145, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181436395, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181436596, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181437137, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749048181437832, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181438120, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181438230, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749048181438626, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749048181439160, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181439386, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181440069, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181440625, "dur": 61365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181503479, "dur": 283, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1749048181503762, "dur": 1157, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1749048181504919, "dur": 72, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.43f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1749048181501992, "dur": 3007, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181505000, "dur": 15292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181520304, "dur": 2516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749048181522822, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181522887, "dur": 2340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749048181525227, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181525322, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749048181527703, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181527811, "dur": 2318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749048181530129, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181530400, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749048181530515, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749048181530663, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749048181530912, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749048181531372, "dur": 239627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181395711, "dur": 26157, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181421877, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D282260B0083D8AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181422355, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181422412, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_52C906CE0C7BEE08.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181422566, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_3397DEB3C6437C6C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181422773, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F21D3FF6DC4634B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181422824, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181422934, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_CB49B96AA053BB61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423033, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_CB49B96AA053BB61.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423098, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_8548D09514643BAC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423413, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423520, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423732, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423838, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181423950, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181424140, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1749048181424398, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181424474, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181424538, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749048181424608, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181424676, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181425261, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181425898, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181426485, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181427073, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181427348, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181427770, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181428321, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181428580, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181429306, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181429799, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181430658, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181430880, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181431142, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181431391, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181431856, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181432081, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181432800, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181433719, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181434401, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181434737, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181435501, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181436124, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181436380, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181436561, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181437515, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181438209, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181438422, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181439025, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181439089, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181439195, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181439436, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181440455, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181440619, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181440773, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181441223, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181441388, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181441599, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181442319, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181442445, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181442656, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181443474, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181443586, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181443757, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181444290, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181444390, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181444558, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181444965, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181445072, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749048181445274, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181446231, "dur": 63, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181446938, "dur": 168908, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181620142, "dur": 10393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749048181619784, "dur": 10822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181630607, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181630700, "dur": 14731, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749048181630697, "dur": 16400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181648773, "dur": 189, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749048181649404, "dur": 94836, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749048181765306, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749048181765297, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749048181765417, "dur": 5443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181395663, "dur": 26177, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181421850, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EB311F2080B4776.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181422346, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181422438, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_54FADE420F1D3CF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181422826, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181422931, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0368709244A973A5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181423262, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181423610, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749048181423898, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181424333, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749048181424581, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749048181424679, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181424783, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749048181424857, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181425045, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181425243, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181425957, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181426715, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181427051, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181427298, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181427784, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181428643, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181429282, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181429993, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181430241, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181430476, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181430743, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181431008, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181431263, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181431791, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181432016, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181432224, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181432586, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181433131, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181433708, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181434493, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181434857, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181435513, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181436148, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181436341, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181436437, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181437269, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181437570, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181437770, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181438230, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749048181438363, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181438456, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181438923, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749048181439175, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181439977, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181440211, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181440658, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181440733, "dur": 79530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181520265, "dur": 2485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181522750, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181522825, "dur": 2584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181525412, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181527780, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181527896, "dur": 2229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749048181530126, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181530388, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181530915, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749048181531578, "dur": 239333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181395657, "dur": 26172, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181421844, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181421951, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_17C8569D27D04AA9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181422410, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E1FDEA3861C839C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181422813, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181423018, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4A1C632ABDE44C9A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181423335, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181423772, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749048181423963, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749048181424114, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181424567, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749048181424653, "dur": 659, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749048181425313, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181426052, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181426601, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181426910, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181427170, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181427993, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181428359, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181428623, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181429197, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181429401, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181429902, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181430164, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181430855, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181431096, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181431335, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181431815, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181432047, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181432286, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181432876, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181433377, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181433919, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181434445, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181434982, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181435503, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181436119, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181436297, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181436357, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181438445, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181438919, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181439110, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181439160, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181439239, "dur": 1346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181440586, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181440847, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181441010, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181442287, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181442425, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749048181442610, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181443099, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181443259, "dur": 77134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181520407, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181523092, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181523177, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181525231, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181525326, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181528026, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749048181530326, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749048181530668, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749048181530836, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749048181530968, "dur": 239881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181395761, "dur": 26151, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181421922, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_BD2F93032BE698E4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181422381, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_DE30F0D9293C6825.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181422432, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181422833, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181423065, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A821154284A45A04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181423263, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181424409, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181424561, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181424649, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749048181425038, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181425680, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181426936, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181427268, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181427851, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181428250, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181428464, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181428658, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181429565, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181430157, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181430370, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181430821, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181431041, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181431276, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181432117, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181432583, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181433311, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181433874, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181434410, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181434745, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181435515, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181436132, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181436765, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181436830, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181437344, "dur": 690, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181438086, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181438307, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181438380, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181438915, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181439534, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749048181439779, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181440249, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181440335, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181440628, "dur": 79638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181520267, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181522733, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181522855, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181522926, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181525290, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181527817, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181527930, "dur": 2576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181530507, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181530559, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749048181530939, "dur": 234364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749048181765324, "dur": 5426, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749048181765305, "dur": 5447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749048181395679, "dur": 26175, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181421864, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_1E332B821429B7DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181422112, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181422340, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7AD711F5F88D031D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181422463, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181422518, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181422597, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8670CD93B0BDDB1D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181422836, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_638C1D2B92C50D00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181423050, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0B3C92E79B34D00C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181423117, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E79D22299C4825D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181423168, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181423219, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E79D22299C4825D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181423339, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181423567, "dur": 8360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181431928, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181432349, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181432524, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181432594, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181435393, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181435559, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181435679, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181436122, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181436289, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181436368, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181437106, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181437789, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181437880, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181438367, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181438878, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181438948, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181439091, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181439210, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181439910, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181440073, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181440603, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749048181440766, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181441216, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181441340, "dur": 81453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181522795, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181525167, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181525298, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181527792, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181527949, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749048181530282, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181530517, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749048181530647, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181530715, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749048181530796, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749048181530854, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SingularityGroup.HotReload.Runtime.Public.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749048181530963, "dur": 239892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181395727, "dur": 26153, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181421890, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1A6A5C7C900791C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181422359, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_774893DE4F1F8941.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181422426, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181422495, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4D5EF4ECE256D09B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181422816, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9A864A99F028DF6B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181423276, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_FE7D9C1FEAD7EA33.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181423421, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181423581, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181423688, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749048181423800, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749048181424138, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749048181424393, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749048181424516, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749048181424709, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181425227, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181426057, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181426715, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181426925, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181427181, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181427682, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181428374, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181428680, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181429775, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181430384, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181430657, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181430889, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181431135, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181431360, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181431976, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181432187, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181432425, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181433087, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181433665, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181434239, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181434709, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181435526, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181436140, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181436348, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181437489, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181437681, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181437844, "dur": 1455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181439300, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181439433, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181440052, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181440605, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749048181440723, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181440778, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181441227, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181441391, "dur": 78869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181520262, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181522712, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181522777, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181522892, "dur": 2664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181525557, "dur": 1227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749048181526794, "dur": 2151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181528996, "dur": 2290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749048181531360, "dur": 239584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181395732, "dur": 26162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181421905, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_54DFB522ED918165.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181422335, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181422432, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_13ED6B36C70FAB0A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181422657, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_5E49D6BEFCDFF46D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181422709, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181422778, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_ADF67561AEE86815.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181422954, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181423055, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2039BDFA7AABC76E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181423168, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181423294, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749048181423402, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749048181424018, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749048181424335, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749048181424520, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749048181424586, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181424664, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749048181425212, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181425980, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181426890, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181427132, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181427373, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181427904, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181428331, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181429161, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181429368, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181429914, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181430159, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181430449, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181430698, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181431174, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181431506, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181431766, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181431989, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181432235, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181432916, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181433609, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181434142, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181434335, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181434748, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181435533, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181436153, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181436380, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181436779, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181437434, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181437663, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181437888, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181438335, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181438850, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181439151, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181439224, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181440074, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181440601, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181440829, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181441307, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181441415, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181441473, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181441683, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181442293, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181442520, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749048181442646, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181442984, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181443117, "dur": 77244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181520370, "dur": 2460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181522831, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181522915, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181525361, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181525441, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181525712, "dur": 2511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181528229, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181528318, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749048181530626, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181530731, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749048181530827, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749048181530936, "dur": 222011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749048181752973, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749048181752948, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749048181753154, "dur": 1501, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749048181754659, "dur": 16302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181395810, "dur": 26142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181421966, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B95C3234EB0BE89D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181422569, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0C1C2D14907EB90E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181422702, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_84BF5D8C55DA5D3D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181422827, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181423133, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4D1C445C9CC0E084.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181423196, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181423466, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749048181423828, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749048181424197, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749048181424445, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749048181424519, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749048181424663, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749048181424889, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181425257, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181425978, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181426792, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181427016, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181427297, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181427822, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181428083, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181428412, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181428602, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181429214, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181429712, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181430183, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181430618, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181430852, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181431125, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181431337, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181431843, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181432069, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181432352, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181432409, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181433042, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181433786, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181434293, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181434719, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181435519, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181436141, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181436320, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181436582, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181437274, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181437449, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181437672, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181438361, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181438552, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181438921, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181439105, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181439846, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181439959, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181440081, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181440619, "dur": 1824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181442445, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749048181442659, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181443177, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181443303, "dur": 77072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181520388, "dur": 2294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181522684, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181522779, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181522879, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181525208, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181525457, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749048181527810, "dur": 2640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749048181530742, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749048181530987, "dur": 239860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181395799, "dur": 26131, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181421943, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8F85FFFDEEDD670C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181422473, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_30E5849A6622DFB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181422523, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181422597, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_12B7B81D94FFC9B7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181422697, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C49902DAED8AF95B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181422822, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181422925, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_42313A2B468B0196.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181423145, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_31DA0AEF6717D1CD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181423648, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749048181423769, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749048181424138, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749048181424281, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749048181424515, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749048181424693, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181424991, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181425130, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181425250, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181426032, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181426932, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181427166, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181427812, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181428261, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181428477, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181428685, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181429487, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181430126, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181430329, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181430547, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181430818, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181431061, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181431306, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181432147, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181432386, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181433152, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181433834, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181434318, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181434667, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181435500, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181436121, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181436459, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181437718, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181437925, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181438231, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181439138, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181439197, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749048181439394, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181439953, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181440049, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1749048181440544, "dur": 107, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181441031, "dur": 72833, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1749048181520267, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181522730, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181522807, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181522913, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181525205, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181525301, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181527678, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181527891, "dur": 2148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749048181530040, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181530392, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181530511, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749048181530660, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749048181530733, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749048181530918, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749048181531978, "dur": 238909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181395853, "dur": 26118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181421981, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1EA6BBE2456BF314.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181422428, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181422501, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8E3C9D84E1638511.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181422794, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7CFFD4AD9CA821AB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181422882, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_9C52184911B1A4E6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181423703, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749048181423879, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749048181424285, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749048181424389, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749048181424608, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181424659, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749048181424899, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6572595573279557027.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749048181425218, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181426007, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181426589, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181427204, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181427736, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181428314, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181428515, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181429056, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181429275, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181429889, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181430203, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181430425, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181430682, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181431179, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181432001, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181432280, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181432846, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181433434, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181434070, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181434352, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181434751, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181435509, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181436337, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181436714, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749048181437986, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181438207, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181438270, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181439200, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181439521, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181439602, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749048181440103, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181440213, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181440622, "dur": 4456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181445080, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749048181445269, "dur": 75037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181520318, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749048181522752, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181522865, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749048181525113, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749048181527943, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181528146, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749048181530698, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749048181530806, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1749048181531000, "dur": 240030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181395883, "dur": 26102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181421995, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_2BF08392AAC9040F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181422429, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181422825, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_D05A0C289995407B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181422979, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181423099, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_EF67977AD200318E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181423267, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181423478, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749048181423798, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749048181423962, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749048181424505, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749048181424810, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181425001, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749048181425327, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181426532, "dur": 2103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181428635, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181429291, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181429845, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181430162, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181430392, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181430653, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181430927, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181431312, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181432021, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181432281, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181432762, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181433361, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181433881, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181434482, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181435036, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181435499, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181436124, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181436281, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181436365, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181436991, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181437391, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181437629, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181437684, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181438351, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181438438, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181439154, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181439229, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181440055, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181440614, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181440855, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749048181441059, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181442171, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181442337, "dur": 78074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181520422, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181523215, "dur": 1243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181524469, "dur": 2453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181526923, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181527832, "dur": 2280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181530169, "dur": 1699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749048181531869, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749048181531961, "dur": 238936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181395911, "dur": 26087, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181422011, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_62617522B0406703.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181422497, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181422648, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DDBE5B8EC557ADE5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181422806, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_DA6971D49F67486A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181422977, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181423077, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_36EDDF0496264E2B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181423477, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181423672, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749048181423914, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749048181424092, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749048181424205, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1749048181424337, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749048181424861, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181425070, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749048181425288, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181425522, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181426613, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181427495, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181428076, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181428306, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181428497, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181428691, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181429497, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181429989, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181430231, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181430451, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181430695, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181430933, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181431189, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181431817, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181432119, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181432809, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181433296, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181433814, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181434672, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181435523, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181436327, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181437039, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181438101, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181438239, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181439119, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181439199, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749048181439390, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181439612, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181440058, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181440219, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181440626, "dur": 64380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181505007, "dur": 15265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181520284, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181522836, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181522910, "dur": 2318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181525287, "dur": 2630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181527965, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181530441, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181530518, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181530935, "dur": 88856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181619816, "dur": 25613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749048181619794, "dur": 27252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181648368, "dur": 187, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749048181649294, "dur": 79318, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749048181752941, "dur": 9425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749048181752931, "dur": 9437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749048181762394, "dur": 734, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749048181763135, "dur": 7739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181395947, "dur": 26071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181422029, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EC09CF01092E25EE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181422465, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181422596, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7C852BDA8365271E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181422828, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181422947, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_03ECB0133E6B6EED.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181423145, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0F4B26DD7F6C67C4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181423465, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181423669, "dur": 7729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181431400, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181431657, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181431929, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181432148, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181432447, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181433099, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181433668, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181434155, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181434345, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181434763, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181435524, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181436145, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181436369, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181436474, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181437278, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181437401, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181437463, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181437808, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181438597, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181438702, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181438825, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181438962, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181439109, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181439831, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181440085, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181440622, "dur": 2972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181443596, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749048181443739, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181444102, "dur": 76245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181520358, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181522744, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181522810, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181522983, "dur": 2440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181525424, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181525677, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181528340, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749048181529117, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749048181531548, "dur": 239382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181395977, "dur": 26058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181422044, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181422502, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181422573, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_D6255BE4E0347441.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181422692, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181422792, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E7EDEE91A237FB4D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181422871, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181423008, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181423062, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_F82B715478DE33F3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181423543, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749048181424378, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749048181424611, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181424701, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181424758, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749048181424932, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749048181425076, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749048181425205, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181425458, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181426673, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181427340, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181428186, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181428399, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181428596, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181429280, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181429812, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181430377, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181430711, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181430943, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181431218, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181431847, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181432074, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181432539, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181433118, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181433565, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181434207, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181434339, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181434675, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181435512, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181436149, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181436361, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181437976, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181438392, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181438618, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181439150, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181439270, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181440058, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181440617, "dur": 1829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181442447, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181442607, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181443799, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181443885, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181444006, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181444639, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749048181444746, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181445092, "dur": 75240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181520346, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181522708, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181522786, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181525040, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181525153, "dur": 2422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181527576, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181527900, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749048181530254, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749048181530607, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749048181530806, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1749048181531017, "dur": 239997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181396027, "dur": 26021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181422049, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_F7B8A20762668D38.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181422592, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_233FB08ADCB5306F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181422704, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_10ADE42C79389F20.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181422818, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181422972, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_8AB40BFC1B6A944F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181423041, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_D77C7CCB1D31CDA5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181423605, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749048181423773, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749048181423860, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749048181424063, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749048181424408, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181424464, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749048181424598, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181424814, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181425037, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181425513, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181426098, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181426845, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181427047, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181427331, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181427900, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181428180, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181428379, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181428582, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181429248, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181429739, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181430196, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181430423, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181430675, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181430908, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181431142, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181431334, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181431950, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181432165, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181432418, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181433135, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181433710, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181434300, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181434666, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181435502, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181436140, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181436342, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181436400, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181437116, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181437300, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181438117, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181438542, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181438952, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181439158, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181439847, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181440359, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181440614, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749048181440805, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181441382, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181441517, "dur": 79039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749048181520564, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181523420, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181525849, "dur": 5028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749048181530949, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1749048181531024, "dur": 239953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749048181780285, "dur": 2267, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5696, "tid": 44, "ts": 1749048181801724, "dur": 5347, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5696, "tid": 44, "ts": 1749048181807109, "dur": 1267, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5696, "tid": 44, "ts": 1749048181797111, "dur": 12272, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}