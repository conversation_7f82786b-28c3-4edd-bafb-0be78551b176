using UnityEngine;

public class Reward : MonoBehaviour
{
    [SerializeField] private float reward_speed = 1f;

    [SerializeField] private float max_health = 100f;
    [SerializeField] private float current_health = 100f;

    void Awake()
    {
        current_health = max_health;
    }

    // Update is called once per frame
    void Update()
    {
        transform.Translate(Vector3.back * reward_speed * Time.deltaTime);
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.tag == "Projectile")
        {
            current_health -= 10f;
        }

        if (current_health <= 0)
        {
            Player_Number_Reward();
            Destroy(gameObject);
        }
    }

    void Player_Number_Reward()
    {
        RadialFormation radialFormation = GameObject.Find("Player").GetComponent<RadialFormation>();
        radialFormation._amount += 4;
    }
}
