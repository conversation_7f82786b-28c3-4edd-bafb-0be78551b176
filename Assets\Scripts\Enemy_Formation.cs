using UnityEngine;

public class Enemy_Formation : MonoBehaviour
{
    [Header("Formation Settings")]
    public Vector3 player_offset;
    public float enemy_circle_radius = 3f;
    public float enemy_circle_distance = 0.5f; // Angle between enemies in radians

    [Header("Multi-Ring Settings")]
    public int max_enemies_per_ring = 6; // Maximum enemies in each ring
    public float ring_radius_increment = 2f; // How much larger each ring is
    public int max_rings = 3; // Maximum number of rings
    int enemyIndex = 0;

    public Vector3 GetPosition()
    {
        // Calculate which ring this enemy belongs to
        int ringIndex = enemyIndex / max_enemies_per_ring;
        int positionInRing = enemyIndex % max_enemies_per_ring;

        // Clamp ring index to maximum rings
        ringIndex = Mathf.Min(ringIndex, max_rings - 1);

        // Calculate radius for this ring
        float currentRadius = enemy_circle_radius + (ringIndex * ring_radius_increment);

        // Calculate angle for this position in the ring
        float angle = enemy_circle_distance * positionInRing;

        // Calculate position
        Vector3 position = new(
            player_offset.x + currentRadius * Mathf.Cos(angle),
            0f,
            player_offset.z + currentRadius * Mathf.Sin(angle)
        );

        enemyIndex++;

        if(enemyIndex >= GetMaxEnemies())
        {
            enemyIndex = 0;
        }

        return position;
    }

    // Get information about which ring an enemy is in
    public int GetRingIndex(int enemyIndex)
    {
        return Mathf.Min(enemyIndex / max_enemies_per_ring, max_rings - 1);
    }

    // Get the radius of a specific ring
    public float GetRingRadius(int ringIndex)
    {
        return enemy_circle_radius + (ringIndex * ring_radius_increment);
    }

    // Get the maximum number of enemies that can fit in all rings
    public int GetMaxEnemies()
    {
        return max_enemies_per_ring * max_rings;
    }

    // Get how many enemies are in a specific ring (currently all rings have same capacity)
    public int GetEnemiesInRing(int ringIndex)
    {
        // For now, all rings have the same capacity
        // You could modify this to have different capacities per ring if needed
        if (ringIndex >= 0 && ringIndex < max_rings)
        {
            return max_enemies_per_ring;
        }
        return 0;
    }
}
