using UnityEngine;

public class Enemy_Formation : MonoBeh<PERSON>our
{
    public Vector3 player_offset;
    public float enemy_circle_radius;
    public float enemy_circle_distance;

    public Vector3 getPosition(int i)
    {
        Vector3 position = new Vector3(player_offset.x + enemy_circle_radius * Mathf.Cos(enemy_circle_distance * i), 0f, player_offset.z + enemy_circle_radius * Mathf.Sin(Mathf.PI * enemy_circle_distance * i));
        return position;
    }
}
