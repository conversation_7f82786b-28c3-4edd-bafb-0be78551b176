using UnityEngine;

public class Enemy : MonoBeh<PERSON>our
{
    [SerializeField] private float enemy_speed = 1f;
    [SerializeField] private float max_health = 100f;
    [SerializeField] private float current_health = 100f;

    [HideInInspector] public bool player_found = false;
    [HideInInspector] public GameObject player;
    [SerializeField] private float player_distance_offset;

    void Awake()
    {
        current_health = max_health;
    }

    void Update()
    {
        if (player_found && player != null)
        {
            // Calculate the direction from enemy to player
            Vector3 directionToPlayer = (player.transform.position - transform.position).normalized;

            // Calculate the desired position (behind the player by the offset distance)
            Vector3 desiredPosition = player.transform.position - directionToPlayer * player_distance_offset;

            // Calculate the distance to the desired position
            float distanceToDesiredPosition = Vector3.Distance(transform.position, desiredPosition);

            // Only move if we're too far from the desired position
            if (distanceToDesiredPosition > 0.1f) // Small threshold to prevent jittering
            {
                // Move towards the desired position (behind the player)
                Vector3 moveDirection = (desiredPosition - transform.position).normalized;
                transform.Translate(moveDirection * enemy_speed * Time.deltaTime);
            }
        }
        else
        {
            // No player found, move forward as before
            transform.Translate(Vector3.back * enemy_speed * Time.deltaTime);
        }
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.tag == "Projectile")
        {
            current_health -= 10f;
        }

        if (current_health <= 0)
        {
            Destroy(gameObject);
        }
    }
}
