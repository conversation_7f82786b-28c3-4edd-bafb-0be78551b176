using UnityEngine;

public class Enemy : MonoBehaviour
{
    [SerializeField] private float enemy_speed = 1f;
    [SerializeField] private float max_health = 100f;
    [SerializeField] private float current_health = 100f;

    [HideInInspector] public bool player_found = false;
    [HideInInspector] public GameObject player;
    [SerializeField] private float player_distance_offset;

    void Awake()
    {
        current_health = max_health;
    }

    void Update()
    {
        if (player_found && player != null)
        {
            // Calculate the distance to the player
            float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);

            // If we're too close to the player, move backward to create lagging effect
            if (distanceToPlayer < player_distance_offset)
            {
                // Move backward (same direction as platform) but slower to create lagging effect
                transform.Translate(Time.deltaTime * enemy_speed * Vector3.back);
            }
            // If we're too far from the player, move forward to catch up
            else if (distanceToPlayer > player_distance_offset + 1f) // Add buffer to prevent oscillation
            {
                // Move forward to catch up with the player
                transform.Translate(Time.deltaTime * enemy_speed * Vector3.forward);
            }
            // If we're at the right distance, move backward but slower than platform to maintain lag
            else
            {
                // Move backward at a slower speed to maintain the lagging effect
                // This should be slower than the platform speed to create the illusion
                float lagSpeed = enemy_speed * 0.7f; // Move 70% of normal speed to lag behind
                transform.Translate(Time.deltaTime * lagSpeed * Vector3.back);
            }
        }
        else
        {
            // No player found, move forward as before
            transform.Translate(Time.deltaTime * enemy_speed * Vector3.back);
        }
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.tag == "Projectile")
        {
            current_health -= 10f;
        }

        if (current_health <= 0)
        {
            Destroy(gameObject);
        }
    }
}
