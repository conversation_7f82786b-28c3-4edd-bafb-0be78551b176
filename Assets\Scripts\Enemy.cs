using UnityEngine;

public class Enemy : MonoBehaviour
{
    public int enemy_index = 1;
    [SerializeField] private float enemy_speed = 1f;
    [SerializeField] private float max_health = 100f;
    [SerializeField] private float current_health = 100f;

    [HideInInspector] public bool player_found = false;
    [HideInInspector] public GameObject player;
    [SerializeField] private float player_distance_offset;
    [SerializeField] private Vector3 player_offset;
    private Enemy_Formation enemy_Formation;
    private Vector3 enemy_position_offset;

    void Awake()
    {
        enemy_Formation = transform.parent.GetComponent<Enemy_Formation>();
        enemy_position_offset = enemy_Formation.getPosition(enemy_index);
        current_health = max_health;
    }

    void Update()
    {
        transform.Translate(player_found ? (player.transform.position - transform.position).magnitude > player_distance_offset ? (player.transform.position + player_offset + enemy_Formation.getPosition(enemy_index) - transform.position).normalized * enemy_speed * Time.deltaTime : Vector3.zero : Vector3.back * enemy_speed * Time.deltaTime);
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.tag == "Projectile")
        {
            current_health -= 10f;
        }

        if (current_health <= 0)
        {
            Destroy(gameObject);
        }
    }

    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "Player")
        {
            player = other.gameObject;
            player_found = true;
        }
    }
}
