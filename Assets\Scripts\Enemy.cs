using UnityEngine;

public class Enemy : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
{
    [Head<PERSON>("Enemy Settings")]
    [SerializeField] private float enemy_speed = 1f;
    [SerializeField] private float max_health = 100f;
    [SerializeField] private float current_health = 100f;

    [HideInInspector] public bool player_found = false;
    [HideInInspector] public GameObject player;
    [SerializeField] private float player_distance_offset;
    [SerializeField] private Vector3 player_offset;
    private Enemy_Formation enemy_Formation;
    private Vector3 enemy_position;

    void Awake()
    {
        enemy_Formation = transform.parent.GetComponent<Enemy_Formation>();
        current_health = max_health;
    }

    void Update()
    {
        if (player_found && player != null)
        {
            // Get the current formation position for this enemy
            Vector3 targetPosition = player.transform.position + player_offset + enemy_position;
            //Vector3 targetPosition = player.transform.position + enemy_Formation.GetPosition(enemy_index);

            // Calculate distance to target position
            float distanceToTarget = Vector3.Distance(transform.position, targetPosition);

            if (distanceToTarget > player_distance_offset)
            {
                // Move towards the formation position
                Vector3 moveDirection = (targetPosition - transform.position).normalized;
                transform.Translate(Time.deltaTime * enemy_speed * moveDirection);
            }
            else
            {
                // At formation position, move backward to maintain lag effect
                transform.Translate(Time.deltaTime * enemy_speed * 0.7f * Vector3.back);
            }
        }
        else
        {
            // No player found, move forward as before
            transform.Translate(Time.deltaTime * enemy_speed * Vector3.back);
        }
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.CompareTag("Projectile"))
        {
            current_health -= 10f;
        }

        if (current_health <= 0)
        {
            Destroy(gameObject);
        }
    }

    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player") && !player_found)
        {
            enemy_position = enemy_Formation.GetPosition();
            player = other.gameObject;
            player_found = true;
        }
    }
}
